//! 进程执行工具
//! 对应原文件: src/scripts/exec.mjs

use std::process::{Command as StdCommand, Stdio};
use tokio::process::Command;
use anyhow::Result;
use tracing::{debug, error};

/// 执行结果 (对应 PromiseExec 的返回值)
#[derive(Debug, Clone)]
pub struct ExecResult {
    pub stdout: String,
    pub stderr: String,
    pub status: i32,
}

/// 执行命令（异步版本，对应 PromiseExec）
pub async fn exec(command: &str) -> Result<ExecResult> {
    debug!("Executing command (async): {}", command);

    let output = if cfg!(target_os = "windows") {
        Command::new("cmd")
            .args(["/C", command])
            .output()
            .await?
    } else {
        Command::new("sh")
            .args(["-c", command])
            .output()
            .await?
    };

    let result = ExecResult {
        stdout: String::from_utf8_lossy(&output.stdout).to_string(),
        stderr: String::from_utf8_lossy(&output.stderr).to_string(),
        status: output.status.code().unwrap_or(-1),
    };

    if result.status != 0 {
        error!("Command failed with status {}: {}", result.status, result.stderr);
    }

    Ok(result)
}

/// 执行命令（同步版本）
pub fn exec_sync(command: &str) -> Result<ExecResult> {
    debug!("Executing command (sync): {}", command);

    let output = if cfg!(target_os = "windows") {
        StdCommand::new("cmd")
            .args(["/C", command])
            .output()?
    } else {
        StdCommand::new("sh")
            .args(["-c", command])
            .output()?
    };

    let result = ExecResult {
        stdout: String::from_utf8_lossy(&output.stdout).to_string(),
        stderr: String::from_utf8_lossy(&output.stderr).to_string(),
        status: output.status.code().unwrap_or(-1),
    };

    if result.status != 0 {
        error!("Command failed with status {}: {}", result.status, result.stderr);
    }

    Ok(result)
}

/// 执行命令并只返回stdout（成功时）
pub async fn exec_stdout(command: &str) -> Result<String> {
    let result = exec(command).await?;
    if result.status == 0 {
        Ok(result.stdout.trim().to_string())
    } else {
        Err(anyhow::anyhow!("Command failed: {}", result.stderr))
    }
}
