//! 环境检测工具
//! 对应原文件: src/scripts/env.mjs

use sysinfo::{System, SystemExt};
use std::path::Path;
use std::sync::OnceLock;

/// 检测是否在Docker环境中运行 (对应 in_docker)
pub fn in_docker() -> bool {
    static IN_DOCKER: OnceLock<bool> = OnceLock::new();

    *IN_DOCKER.get_or_init(|| {
        // 只在Linux平台检测
        if !cfg!(target_os = "linux") {
            return false;
        }

        // 检查Docker环境标识文件
        if Path::new("/.dockerenv").exists() {
            return true;
        }

        // 检查cgroup信息
        if let Ok(cgroup_content) = std::fs::read_to_string("/proc/1/cgroup") {
            return cgroup_content.contains("docker") || cgroup_content.contains("containerd");
        }

        false
    })
}

/// 检测是否在Termux环境中运行 (对应 in_termux)
pub fn in_termux() -> bool {
    static IN_TERMUX: OnceLock<bool> = OnceLock::new();

    *IN_TERMUX.get_or_init(|| {
        // 只在Linux平台检测
        if !cfg!(target_os = "linux") {
            return false;
        }

        // 检查Termux特有目录
        Path::new("/data/data/com.termux").exists()
    })
}

/// 获取系统信息
pub fn get_system_info() -> SystemInfo {
    let sys = System::new_all();

    SystemInfo {
        os: std::env::consts::OS.to_string(),
        arch: std::env::consts::ARCH.to_string(),
        total_memory: sys.total_memory(),
        available_memory: sys.available_memory(),
        cpu_count: sys.cpus().len(),
    }
}

/// 检测是否在CI环境中
pub fn in_ci() -> bool {
    std::env::var("CI").is_ok() ||
    std::env::var("CONTINUOUS_INTEGRATION").is_ok() ||
    std::env::var("GITHUB_ACTIONS").is_ok() ||
    std::env::var("GITLAB_CI").is_ok()
}

/// 检测是否在开发环境中
pub fn in_development() -> bool {
    std::env::var("NODE_ENV").map_or(false, |env| env == "development") ||
    std::env::var("RUST_ENV").map_or(false, |env| env == "development") ||
    cfg!(debug_assertions)
}

/// 检测是否在生产环境中
pub fn in_production() -> bool {
    std::env::var("NODE_ENV").map_or(false, |env| env == "production") ||
    std::env::var("RUST_ENV").map_or(false, |env| env == "production") ||
    (!cfg!(debug_assertions) && !in_development())
}

#[derive(Debug)]
pub struct SystemInfo {
    pub os: String,
    pub arch: String,
    pub total_memory: u64,
    pub available_memory: u64,
    pub cpu_count: usize,
}
