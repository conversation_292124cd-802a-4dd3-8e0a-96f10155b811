//! 国际化工具
//! 对应原文件: src/scripts/i18n.mjs

use fluent::{FluentBundle, FluentResource};
use unic_langid::LanguageIdentifier;
use std::collections::HashMap;
use anyhow::Result;

/// 国际化管理器
pub struct I18nManager {
    bundles: HashMap<String, FluentBundle<FluentResource>>,
    current_locale: String,
}

impl I18nManager {
    /// 创建新的国际化管理器
    pub fn new() -> Self {
        Self {
            bundles: HashMap::new(),
            current_locale: "en-US".to_string(),
        }
    }
    
    /// 加载语言包
    pub fn load_locale(&mut self, locale: &str, content: &str) -> Result<()> {
        let resource = FluentResource::try_new(content.to_string())
            .map_err(|_| anyhow::anyhow!("Failed to parse fluent resource"))?;
        
        let lang_id: LanguageIdentifier = locale.parse()
            .map_err(|_| anyhow::anyhow!("Invalid language identifier"))?;
        
        let mut bundle = FluentBundle::new(vec![lang_id]);
        bundle.add_resource(resource)
            .map_err(|_| anyhow::anyhow!("Failed to add resource to bundle"))?;
        
        self.bundles.insert(locale.to_string(), bundle);
        Ok(())
    }
    
    /// 设置当前语言
    pub fn set_locale(&mut self, locale: &str) {
        self.current_locale = locale.to_string();
    }
    
    /// 获取翻译文本
    pub fn t(&self, key: &str) -> String {
        if let Some(bundle) = self.bundles.get(&self.current_locale) {
            if let Some(msg) = bundle.get_message(key) {
                if let Some(pattern) = msg.value() {
                    let mut errors = vec![];
                    return bundle.format_pattern(pattern, None, &mut errors);
                }
            }
        }
        key.to_string()
    }

    /// 获取可用语言列表
    pub fn available_locales(&self) -> Vec<String> {
        self.bundles.keys().cloned().collect()
    }

    /// 获取当前语言
    pub fn current_locale(&self) -> &str {
        &self.current_locale
    }
}

impl Default for I18nManager {
    fn default() -> Self {
        Self::new()
    }
}

/// 全局国际化管理器
static GLOBAL_I18N: std::sync::OnceLock<std::sync::Mutex<I18nManager>> = std::sync::OnceLock::new();

/// 获取全局国际化管理器
pub fn i18n() -> &'static std::sync::Mutex<I18nManager> {
    GLOBAL_I18N.get_or_init(|| std::sync::Mutex::new(I18nManager::new()))
}

/// 便捷函数：获取翻译文本
pub fn t(key: &str) -> String {
    if let Ok(manager) = i18n().lock() {
        manager.t(key)
    } else {
        key.to_string()
    }
}

/// 便捷函数：设置语言
pub fn set_locale(locale: &str) -> Result<()> {
    if let Ok(mut manager) = i18n().lock() {
        manager.set_locale(locale);
        Ok(())
    } else {
        Err(anyhow::anyhow!("Failed to lock i18n manager"))
    }
}
}
