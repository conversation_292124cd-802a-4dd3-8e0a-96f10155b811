# Fount Rust Implementation

这是Fount项目的Rust实现版本，完全对应原JavaScript/TypeScript项目的功能和结构。

## 项目结构

```
rust/
├── Cargo.toml              # Workspace配置
├── crates/                 # 所有crate
│   ├── fount-types/        # 类型定义 (对应 src/decl/)
│   ├── fount-utils/        # 工具库 (对应 src/scripts/)
│   ├── fount-server/       # 服务器核心 (对应 src/server/)
│   └── fount-desktop/      # 桌面应用 (Tauri)
├── static/                 # 静态文件 (对应 src/public/)
├── assets/                 # 资源文件
│   ├── locales/           # 国际化文件
│   └── icons/             # 图标资源
├── scripts/               # 构建和部署脚本
└── docs/                  # 分析和设计文档
```

## 核心组件

### fount-types
- 包含所有类型定义和接口
- 对应原项目的 `src/decl/` 目录
- 提供序列化/反序列化支持

### fount-utils
- 工具函数和实用程序
- 对应原项目的 `src/scripts/` 目录
- 包括国际化、通知、系统集成等功能

### fount-server
- Web服务器核心实现
- 对应原项目的 `src/server/` 目录
- 基于Axum框架构建
- 包含认证、API端点、管理器等

### fount-desktop
- 桌面应用实现
- 基于Tauri框架
- 提供系统托盘、原生集成等功能

## 技术栈

- **Web框架**: Axum
- **异步运行时**: Tokio
- **序列化**: Serde
- **桌面框架**: Tauri
- **模板引擎**: Askama
- **HTTP客户端**: Reqwest
- **认证**: JWT + bcrypt
- **日志**: Tracing
- **配置**: Config crate

## 构建和运行

### 开发环境

```bash
# 安装Rust (如果尚未安装)
curl --proto '=https' --tlsv1.2 -sSf https://sh.rustup.rs | sh

# 克隆项目
cd rust/

# 构建所有组件
cargo build

# 运行服务器
cargo run --bin fount-server

# 运行桌面应用
cargo run --bin fount-desktop
```

### 生产环境

```bash
# 构建发布版本
cargo build --release

# 运行发布版本
./target/release/fount-server
```

## 开发指南

### 添加新功能

1. 在相应的crate中添加类型定义 (fount-types)
2. 实现工具函数 (fount-utils)
3. 添加服务器端点 (fount-server)
4. 更新桌面应用接口 (fount-desktop)

### 测试

```bash
# 运行所有测试
cargo test

# 运行特定crate的测试
cargo test -p fount-server

# 运行集成测试
cargo test --test integration
```

### 代码格式化

```bash
# 格式化代码
cargo fmt

# 检查代码质量
cargo clippy
```

## 部署

### Docker部署

```bash
# 构建Docker镜像
docker build -t fount-rust .

# 运行容器
docker run -p 3000:3000 fount-rust
```

### 系统服务

参考 `scripts/` 目录中的服务配置文件。

## 配置

配置文件支持多种格式：
- `config.toml` (推荐)
- `config.json`
- `config.yaml`

环境变量前缀：`FOUNT_`

示例配置：
```toml
[server]
host = "127.0.0.1"
port = 3000

[auth]
jwt_secret = "your-secret-key"

[logging]
level = "info"
```

## 许可证

MIT License

## 贡献

欢迎提交Issue和Pull Request！

## 文档

详细文档请参考：
- [API文档](docs/api.md)
- [架构设计](docs/architecture.md)
- [部署指南](docs/deployment.md)
