[workspace]
members = [
    "crates/fount-types",
    "crates/fount-utils", 
    "crates/fount-server",
    "crates/fount-desktop"
]
resolver = "2"

[workspace.package]
version = "0.1.0"
edition = "2021"
license = "MIT"
authors = ["Fount Team"]
repository = "https://github.com/steve02081504/fount"
homepage = "https://github.com/steve02081504/fount"
description = "Fount - AI Character Chat Platform"
keywords = ["ai", "chat", "character", "roleplay"]
categories = ["web-programming", "gui"]

[workspace.dependencies]
# 核心运行时
tokio = { version = "1.35.1", features = ["full"] }
serde = { version = "1.0.193", features = ["derive"] }
serde_json = "1.0.108"
anyhow = "1.0.79"
thiserror = "1.0.56"

# 异步工具
futures = "0.3.30"
async-trait = "0.1.77"

# 日志和追踪
tracing = "0.1.40"
tracing-subscriber = { version = "0.3.18", features = ["env-filter", "json"] }
tracing-appender = "0.2.3"

# 时间处理
chrono = { version = "0.4.31", features = ["serde"] }
humantime = "2.1.0"

# 配置管理
config = "0.14.0"
dirs = "5.0.1"

# 加密和安全
bcrypt = "0.15.0"
jsonwebtoken = "9.2.0"
uuid = { version = "1.6.1", features = ["v4", "serde"] }
rand = "0.8.5"

# 网络和HTTP
reqwest = { version = "0.11.23", features = ["json", "stream", "multipart", "cookies", "gzip", "brotli", "rustls-tls"] }
reqwest-middleware = "0.2.4"
reqwest-retry = "0.4.0"
reqwest-tracing = "0.4.7"

# 文件系统
fs_extra = "1.3.0"
walkdir = "2.4.0"
notify = "6.1.1"

# 压缩和归档
zip = "0.6.6"
flate2 = "1.0.28"

# 图像处理
image = { version = "0.24.7", features = ["png", "jpeg", "ico"] }

# 正则表达式
regex = "1.10.2"

# 数据结构
indexmap = "2.1.0"
dashmap = "5.5.3"

# 配置文件格式
toml = "0.8.8"

# Web框架
axum = { version = "0.7.4", features = ["ws", "multipart", "macros"] }
tower = { version = "0.4.13", features = ["full"] }
tower-http = { version = "0.5.0", features = ["fs", "cors", "trace", "compression-gzip", "compression-br"] }
hyper = { version = "1.1.0", features = ["full"] }

# 模板引擎
askama = { version = "0.12.1", features = ["with-axum"] }
askama_axum = "0.4.0"

# 错误监控
sentry = { version = "0.32.1", features = ["tracing", "debug-images"] }
sentry-tower = "0.32.1"
sentry-tracing = "0.32.1"

# 系统集成
notify-rust = "4.10.0"
tray-icon = "0.14.3"
interprocess = "1.2.1"
crossterm = "0.27.0"

# 国际化
fluent = "0.16.0"
fluent-bundle = "0.15.2"
unic-langid = "0.9.4"

# 环境检测
sysinfo = "0.30.5"

# 速率限制
governor = "0.6.0"
nonzero_ext = "0.3.0"

# Discord RPC
discord-rich-presence = "0.2.3"

# 验证码
captcha = "0.0.9"

# WebSocket
tokio-tungstenite = "0.21.0"

# 文件上传
multer = "3.0.0"

# Cookie处理
cookie = "0.18.0"

# MIME类型
mime = "0.3.17"
mime_guess = "2.0.4"

# 单实例检查
single-instance = "0.3.0"

# 信号处理
signal-hook = "0.3.17"
signal-hook-tokio = { version = "0.3.1", features = ["futures-v0_3"] }

# 性能监控
metrics = "0.22.0"
metrics-exporter-prometheus = "0.13.0"

# 内存映射
memmap2 = "0.9.4"

# 动态库加载
libloading = "0.8.1"

# 内部依赖
fount-types = { path = "crates/fount-types" }
fount-utils = { path = "crates/fount-utils" }
fount-server = { path = "crates/fount-server" }
fount-desktop = { path = "crates/fount-desktop" }

[workspace.dev-dependencies]
# 测试
tokio-test = "0.4.3"
mockall = "0.12.1"
wiremock = "0.5.22"
tempfile = "3.8.1"
assert_matches = "1.5.0"

# 基准测试
criterion = { version = "0.5.1", features = ["html_reports"] }

# 代码覆盖率
tarpaulin = "0.27.3"

# 文档测试
doc-comment = "0.3.3"

# 属性测试
proptest = "1.4.0"
quickcheck = "1.0.3"

# 序列化测试
serde_test = "1.0.176"

# HTTP测试
httpmock = "0.7.0"

[profile.dev]
opt-level = 0
debug = true
split-debuginfo = "unpacked"

[profile.release]
opt-level = 3
lto = true
codegen-units = 1
panic = "abort"
strip = true

[profile.release-with-debug]
inherits = "release"
debug = true
strip = false
