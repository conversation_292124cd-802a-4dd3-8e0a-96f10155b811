# Rust
/target/
**/*.rs.bk
Cargo.lock

# IDE
.vscode/
.idea/
*.swp
*.swo

# OS
.DS_Store
Thumbs.db

# Logs
*.log
logs/

# Config
config.toml
config.json
config.yaml
!default/config.json

# Data
data/
*.db
*.sqlite

# Temporary files
tmp/
temp/

# Build artifacts
dist/
build/

# Tauri
crates/fount-desktop/src-tauri/target/
crates/fount-desktop/src-tauri/WixTools/

# Node modules (if any)
node_modules/

# Environment variables
.env
.env.local
