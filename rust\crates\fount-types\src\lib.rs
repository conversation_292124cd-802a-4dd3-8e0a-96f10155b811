//! Fount类型定义库
//! 
//! 这个crate包含了Fount项目中使用的所有核心类型定义，
//! 对应原JavaScript/TypeScript项目的src/decl/目录。

// 基础类型定义
pub mod base_defs;
pub mod char_api;
pub mod ai_source;
pub mod ai_source_generator;
pub mod user_api;
pub mod world_api;
pub mod import_handler_api;
pub mod plugin_api;
pub mod prompt_struct;
pub mod shell_api;

// 重新导出主要类型
pub use base_defs::*;
pub use char_api::*;
pub use ai_source::*;
pub use ai_source_generator::*;
pub use user_api::*;
pub use world_api::*;
pub use import_handler_api::*;
pub use plugin_api::*;
pub use prompt_struct::*;
pub use shell_api::*;

// 通用错误类型
use thiserror::Error;

/// Fount项目的通用错误类型
#[derive(Debug, Error)]
pub enum FountError {
    #[error("IO错误: {0}")]
    Io(#[from] std::io::Error),

    #[error("序列化错误: {0}")]
    Serialization(#[from] serde_json::Error),

    #[error("网络错误: {0}")]
    Network(String),

    #[error("认证错误: {0}")]
    Authentication(String),

    #[error("权限错误: {0}")]
    Permission(String),

    #[error("配置错误: {0}")]
    Configuration(String),

    #[error("数据库错误: {0}")]
    Database(String),

    #[error("插件错误: {0}")]
    Plugin(String),

    #[error("AI源错误: {0}")]
    AiSource(String),

    #[error("角色错误: {0}")]
    Character(String),

    #[error("世界错误: {0}")]
    World(String),

    #[error("未找到: {0}")]
    NotFound(String),

    #[error("内部错误: {0}")]
    Internal(String),

    #[error("其他错误: {0}")]
    Other(#[from] anyhow::Error),
}

/// 通用结果类型
pub type Result<T> = std::result::Result<T, FountError>;
