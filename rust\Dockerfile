# Fount Rust Docker镜像

# 构建阶段
FROM rust:1.75 as builder

WORKDIR /app

# 复制Cargo文件
COPY Cargo.toml Cargo.lock ./
COPY crates/ ./crates/

# 构建项目
RUN cargo build --release --bin fount-server

# 运行阶段
FROM debian:bookworm-slim

# 安装运行时依赖
RUN apt-get update && apt-get install -y \
    ca-certificates \
    && rm -rf /var/lib/apt/lists/*

# 创建应用用户
RUN useradd -r -s /bin/false fount

# 创建应用目录
WORKDIR /app

# 复制构建产物
COPY --from=builder /app/target/release/fount-server ./
COPY static/ ./static/
COPY assets/ ./assets/
COPY default/ ./default/

# 设置权限
RUN chown -R fount:fount /app
USER fount

# 暴露端口
EXPOSE 3000

# 启动命令
CMD ["./fount-server"]
