@echo off
REM Fount Rust版本启动脚本 (Windows)

echo Starting Fount Rust server...

REM 检查是否在正确的目录
if not exist "Cargo.toml" (
    echo Error: Please run this script from the rust directory
    pause
    exit /b 1
)

REM 构建项目
echo Building project...
cargo build --release
if errorlevel 1 (
    echo Build failed!
    pause
    exit /b 1
)

REM 启动服务器
echo Starting server...
cargo run --release --bin fount-server

pause
