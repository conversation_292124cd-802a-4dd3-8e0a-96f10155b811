//! 服务器配置模块
//! 对应原文件: src/server/server.mjs 中的配置相关功能

use serde::{Deserialize, Serialize};
use std::path::PathBuf;
use anyhow::Result;
use tracing::{info, warn, debug};
use fount_utils::json_loader::{load_json_file, save_json_file, load_json_file_if_exists};
use crate::base::{get_config_path, get_default_config_path, get_data_dir, ensure_data_dir};

/// HTTPS配置
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct HttpsConfig {
    pub enabled: bool,
    #[serde(rename = "keyFile")]
    pub key_file: String,
    #[serde(rename = "certFile")]
    pub cert_file: String,
}

impl Default for HttpsConfig {
    fn default() -> Self {
        Self {
            enabled: false,
            key_file: "certs/key.pem".to_string(),
            cert_file: "certs/cert.pem".to_string(),
        }
    }
}

/// 服务器配置 (对应原文件的 config 对象)
#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize)]
pub struct ServerConfig {
    pub host: String,
    pub port: u16,
    pub debug: bool,
    #[serde(rename = "staticDir")]
    pub static_dir: String,
    #[serde(rename = "dataDir")]
    pub data_dir: String,
    #[serde(rename = "logLevel")]
    pub log_level: String,
    pub https: HttpsConfig,
    
    // 数据库配置
    #[serde(rename = "dbPath")]
    pub db_path: Option<String>,
    
    // 认证配置
    #[serde(rename = "jwtSecret")]
    pub jwt_secret: Option<String>,
    #[serde(rename = "sessionSecret")]
    pub session_secret: Option<String>,
    
    // 上传配置
    #[serde(rename = "maxUploadSize")]
    pub max_upload_size: Option<u64>,
    
    // 其他配置
    pub locale: Option<String>,
    pub timezone: Option<String>,
}

impl Default for ServerConfig {
    fn default() -> Self {
        Self {
            host: "127.0.0.1".to_string(),
            port: 3000,
            debug: false,
            static_dir: "src/public".to_string(),
            data_dir: "data".to_string(),
            log_level: "info".to_string(),
            https: HttpsConfig::default(),
            db_path: Some("data/fount.db".to_string()),
            jwt_secret: None,
            session_secret: None,
            max_upload_size: Some(100 * 1024 * 1024), // 100MB
            locale: Some("en-UK".to_string()),
            timezone: Some("UTC".to_string()),
        }
    }
}

impl ServerConfig {
    /// 获取主机URL
    pub fn get_host_url(&self) -> String {
        let protocol = if self.https.enabled { "https" } else { "http" };
        format!("{}://{}:{}", protocol, self.host, self.port)
    }
    
    /// 获取静态文件目录路径
    pub fn get_static_dir_path(&self) -> PathBuf {
        PathBuf::from(&self.static_dir)
    }
    
    /// 获取数据目录路径
    pub fn get_data_dir_path(&self) -> PathBuf {
        PathBuf::from(&self.data_dir)
    }
    
    /// 验证配置
    pub fn validate(&self) -> Result<()> {
        if self.port == 0 {
            return Err(anyhow::anyhow!("Port cannot be 0"));
        }
        
        if self.https.enabled {
            let key_path = PathBuf::from(&self.https.key_file);
            let cert_path = PathBuf::from(&self.https.cert_file);
            
            if !key_path.exists() {
                return Err(anyhow::anyhow!("HTTPS key file not found: {}", key_path.display()));
            }
            
            if !cert_path.exists() {
                return Err(anyhow::anyhow!("HTTPS cert file not found: {}", cert_path.display()));
            }
        }
        
        Ok(())
    }
}

/// 全局配置实例
static GLOBAL_CONFIG: std::sync::OnceLock<std::sync::RwLock<ServerConfig>> = std::sync::OnceLock::new();

/// 获取全局配置 (对应原文件的 config 变量)
pub fn get_config() -> &'static std::sync::RwLock<ServerConfig> {
    GLOBAL_CONFIG.get_or_init(|| {
        std::sync::RwLock::new(ServerConfig::default())
    })
}

/// 加载配置 (对应原文件的 get_config 函数)
pub fn load_config() -> Result<ServerConfig> {
    debug!("Loading server configuration");
    
    // 确保数据目录存在
    ensure_data_dir()?;
    
    let config_path = get_config_path();
    let default_config_path = get_default_config_path();
    
    // 如果配置文件不存在，从默认配置复制
    if !config_path.exists() {
        if default_config_path.exists() {
            info!("Config file not found, copying from default");
            std::fs::copy(&default_config_path, &config_path)?;
        } else {
            info!("Creating default config file");
            let default_config = ServerConfig::default();
            save_json_file(&config_path, &default_config)?;
        }
    }
    
    // 加载配置文件
    let config: ServerConfig = load_json_file(&config_path)
        .unwrap_or_else(|e| {
            warn!("Failed to load config file: {}, using default", e);
            ServerConfig::default()
        });
    
    // 验证配置
    config.validate()?;
    
    // 更新全局配置
    if let Ok(mut global_config) = get_config().write() {
        *global_config = config.clone();
    }
    
    info!("Configuration loaded successfully");
    debug!("Server will listen on {}:{}", config.host, config.port);
    
    Ok(config)
}

/// 保存配置 (对应原文件的 save_config 函数)
pub fn save_config() -> Result<()> {
    let config = get_config().read()
        .map_err(|_| anyhow::anyhow!("Failed to read global config"))?
        .clone();
    
    let config_path = get_config_path();
    save_json_file(&config_path, &config)?;
    
    info!("Configuration saved to {}", config_path.display());
    Ok(())
}

/// 更新配置
pub fn update_config<F>(updater: F) -> Result<()>
where
    F: FnOnce(&mut ServerConfig) -> Result<()>,
{
    let mut config = get_config().write()
        .map_err(|_| anyhow::anyhow!("Failed to write global config"))?;
    
    updater(&mut config)?;
    config.validate()?;
    
    drop(config);
    save_config()
}

/// 获取配置的只读副本
pub fn get_config_clone() -> ServerConfig {
    get_config().read()
        .map(|config| config.clone())
        .unwrap_or_default()
}
