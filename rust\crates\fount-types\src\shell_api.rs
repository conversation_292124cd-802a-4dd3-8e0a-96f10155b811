//! Shell API类型定义
//! 对应原文件: src/decl/shellAPI.ts

use serde::{Deserialize, Serialize};
use std::collections::HashMap;
use uuid::Uuid;
use chrono::{DateTime, Utc};
use async_trait::async_trait;
use crate::{ApiResponse, Status, Info, Locale};

/// Shell路由器参数
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ShellRouterArgs {
    pub router_id: String,
    pub config: HashMap<String, serde_json::Value>,
}

/// Shell信息接口 (对应 interfaces.info)
#[async_trait]
pub trait ShellInfoInterface {
    async fn update_info(&self, locales: Vec<Locale>) -> crate::Result<Info>;
}

/// Shell配置接口 (对应 interfaces.config)
#[async_trait]
pub trait ShellConfigInterface {
    async fn get_data(&self) -> crate::Result<serde_json::Value>;
    async fn set_data(&self, data: serde_json::Value) -> crate::Result<()>;
}

/// Shell调用接口 (对应 interfaces.invokes)
#[async_trait]
pub trait ShellInvokesInterface {
    async fn arguments_handler(&self, user: String, args: Vec<String>) -> crate::Result<()>;
    async fn ipc_invoke_handler(&self, user: String, data: serde_json::Value) -> crate::Result<serde_json::Value>;
}

/// Shell API主接口 (对应 shellAPI_t)
#[async_trait]
pub trait ShellApi {
    /// 获取Shell信息
    fn get_info(&self) -> &Info;

    /// Shell初始化
    async fn init(&mut self) -> crate::Result<()>;

    /// Shell加载
    async fn load(&mut self, args: ShellRouterArgs) -> crate::Result<()>;

    /// Shell卸载
    async fn unload(&mut self, args: ShellRouterArgs) -> crate::Result<()>;

    /// Shell卸载安装
    async fn uninstall(&mut self, reason: String, from: String) -> crate::Result<()>;

    /// 获取信息接口
    fn info_interface(&self) -> Option<&dyn ShellInfoInterface>;

    /// 获取配置接口
    fn config_interface(&self) -> Option<&dyn ShellConfigInterface>;

    /// 获取调用接口
    fn invokes_interface(&self) -> Option<&dyn ShellInvokesInterface>;
}

/// Shell配置
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ShellConfig {
    pub id: Uuid,
    pub name: String,
    pub description: String,
    pub script_path: String,
    pub enabled: bool,
    pub status: Status,
    pub created_at: DateTime<Utc>,
    pub updated_at: DateTime<Utc>,
    pub metadata: HashMap<String, serde_json::Value>,
}

/// Shell执行请求
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ShellExecuteRequest {
    pub shell_id: Uuid,
    pub command: String,
    pub args: Vec<String>,
}

/// Shell执行响应
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ShellExecuteResponse {
    pub output: String,
    pub error: Option<String>,
    pub exit_code: i32,
}

/// Shell响应类型别名
pub type ShellConfigResponse = ApiResponse<ShellConfig>;
pub type ShellConfigListResponse = ApiResponse<Vec<ShellConfig>>;
pub type ShellExecuteApiResponse = ApiResponse<ShellExecuteResponse>;
