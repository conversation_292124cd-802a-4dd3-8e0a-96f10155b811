//! 管理器模块
//! 对应原文件: src/server/managers/index.mjs

pub mod char_manager;
pub mod shell_manager;
pub mod personas_manager;
pub mod world_manager;
pub mod ai_sources_manager;

// 重新导出所有管理器
pub use char_manager::*;
pub use shell_manager::*;
pub use personas_manager::*;
pub use world_manager::*;
pub use ai_sources_manager::*;

use anyhow::Result;
use std::collections::HashMap;
use std::sync::Arc;
use tokio::sync::RwLock;
use tracing::{info, warn, error, debug};

/// 部件类型列表 (对应原文件的 partsList)
pub const PARTS_LIST: &[&str] = &[
    "chars",
    "AIsources",
    "worlds",
    "shells",
    "personas",
];

/// 部件管理器trait
#[async_trait::async_trait]
pub trait PartManager {
    /// 获取部件列表
    async fn get_part_list(&self, username: &str) -> Result<Vec<String>>;

    /// 获取部件详情
    async fn get_part_details(&self, username: &str, name: &str, no_cache: bool) -> Result<serde_json::Value>;

    /// 加载部件
    async fn load_part(&self, username: &str, name: &str) -> Result<()>;

    /// 卸载部件
    async fn unload_part(&self, username: &str, name: &str) -> Result<()>;
}

/// 初始化所有管理器 (对应原文件的初始化逻辑)
pub async fn init_managers() -> Result<()> {
    info!("Initializing managers");

    // 初始化各种管理器
    char_manager::init().await?;
    ai_sources_manager::init().await?;
    world_manager::init().await?;
    shell_manager::init().await?;
    personas_manager::init().await?;

    info!("All managers initialized successfully");
    Ok(())
}
