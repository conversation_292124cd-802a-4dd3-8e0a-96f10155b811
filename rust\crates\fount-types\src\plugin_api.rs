//! 插件API类型定义
//! 对应原文件: src/decl/pluginAPI.ts

use serde::{Deserialize, Serialize};
use std::collections::HashMap;
use uuid::Uuid;
use chrono::{DateTime, Utc};
use async_trait::async_trait;
use crate::{ApiResponse, Status, Info, Locale};

/// 插件信息接口
#[async_trait]
pub trait PluginInfoInterface {
    async fn update_info(&self, locales: Vec<Locale>) -> crate::Result<Info>;
}

/// 插件配置接口
#[async_trait]
pub trait PluginConfigInterface {
    async fn get_data(&self) -> crate::Result<serde_json::Value>;
    async fn set_data(&self, data: serde_json::Value) -> crate::Result<()>;
}

/// 插件API主接口
#[async_trait]
pub trait PluginApi {
    /// 获取插件信息
    fn get_info(&self) -> &Info;

    /// 插件初始化
    async fn init(&mut self) -> crate::Result<()>;

    /// 插件加载
    async fn load(&mut self) -> crate::Result<()>;

    /// 插件卸载
    async fn unload(&mut self) -> crate::Result<()>;

    /// 插件卸载安装
    async fn uninstall(&mut self) -> crate::Result<()>;

    /// 获取信息接口
    fn info_interface(&self) -> Option<&dyn PluginInfoInterface>;

    /// 获取配置接口
    fn config_interface(&self) -> Option<&dyn PluginConfigInterface>;
}

/// 插件信息
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct Plugin {
    pub id: Uuid,
    pub name: String,
    pub description: String,
    pub version: String,
    pub author: String,
    pub enabled: bool,
    pub status: Status,
    pub created_at: DateTime<Utc>,
    pub updated_at: DateTime<Utc>,
    pub metadata: HashMap<String, serde_json::Value>,
}

/// 插件创建请求
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct CreatePluginRequest {
    pub name: String,
    pub description: String,
    pub version: String,
    pub author: String,
    pub metadata: Option<HashMap<String, serde_json::Value>>,
}

/// 插件响应类型别名
pub type PluginResponse = ApiResponse<Plugin>;
pub type PluginListResponse = ApiResponse<Vec<Plugin>>;

/// 插件配置
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct PluginConfig {
    pub settings: serde_json::Value,
    pub permissions: Vec<String>,
}
