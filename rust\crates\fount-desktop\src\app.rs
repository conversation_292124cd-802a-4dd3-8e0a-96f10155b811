//! 桌面应用核心

use tauri::{App, Manager};
use anyhow::Result;

/// 应用设置
pub fn setup_app(app: &mut App) -> Result<()> {
    // 初始化日志
    tracing_subscriber::fmt::init();
    
    // 设置系统托盘
    setup_system_tray(app)?;
    
    // 启动服务器
    start_background_server(app)?;
    
    tracing::info!("Fount desktop application initialized");
    Ok(())
}

/// 设置系统托盘
fn setup_system_tray(app: &mut App) -> Result<()> {
    // TODO: 实现系统托盘设置
    Ok(())
}

/// 启动后台服务器
fn start_background_server(app: &mut App) -> Result<()> {
    let app_handle = app.handle();
    
    tokio::spawn(async move {
        if let Err(e) = run_server().await {
            tracing::error!("Failed to start background server: {}", e);
        }
    });
    
    Ok(())
}

/// 运行服务器
async fn run_server() -> Result<()> {
    // TODO: 启动fount-server
    Ok(())
}
