//! 角色API类型定义
//! 对应原文件: src/decl/charAPI.ts

use serde::{Deserialize, Serialize};
use std::collections::HashMap;
use uuid::Uuid;
use chrono::{DateTime, Utc};
use async_trait::async_trait;
use crate::{ApiResponse, Status, Info, TimeStamp, Locale, Role};

/// 角色状态 (对应 charState_t)
#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct CharState {
    pub init_count: u32,
    pub start_count: u32,
    pub last_start: TimeStamp,
}

impl Default for CharState {
    fn default() -> Self {
        Self {
            init_count: 0,
            start_count: 0,
            last_start: 0,
        }
    }
}

/// 角色初始化参数 (对应 charInit_t)
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct CharInit {
    pub state: CharState,
    pub username: String,
    pub charname: String,
}

/// 聊天回复请求 (简化版本，对应 chatReplyRequest_t)
#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct ChatReplyRequest {
    pub user_id: String,
    pub character_id: Uuid,
    pub message: String,
    pub context: Option<HashMap<String, serde_json::Value>>,
}

/// 聊天回复 (简化版本，对应 chatReply_t)
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ChatReply {
    pub content: String,
    pub role: Role,
    pub timestamp: TimeStamp,
    pub metadata: Option<HashMap<String, serde_json::Value>>,
}

/// 聊天日志条目 (简化版本，对应 chatLogEntry_t)
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ChatLogEntry {
    pub id: Uuid,
    pub role: Role,
    pub content: String,
    pub timestamp: TimeStamp,
    pub user_id: Option<String>,
    pub character_id: Option<Uuid>,
    pub metadata: Option<HashMap<String, serde_json::Value>>,
}

/// 单部分提示 (简化版本，对应 single_part_prompt_t)
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SinglePartPrompt {
    pub role: Role,
    pub content: String,
    pub metadata: Option<HashMap<String, serde_json::Value>>,
}

/// 提示结构 (简化版本，对应 prompt_struct_t)
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct PromptStruct {
    pub parts: Vec<SinglePartPrompt>,
    pub context: Option<HashMap<String, serde_json::Value>>,
}

/// Shell助手数据
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ShellAssistData {
    pub username: String,
    pub user_charname: String,
    pub shelltype: String,
    pub shellhistory: Vec<ShellHistoryEntry>,
    pub pwd: String,
    pub command_now: String,
    pub command_error: String,
    pub rejected_commands: Vec<String>,
    pub chat_scoped_char_memory: HashMap<String, serde_json::Value>,
}

/// Shell历史条目
#[derive(Debug, Clone, Serialize, Deserialize)]
#[serde(untagged)]
pub enum ShellHistoryEntry {
    Command {
        command: String,
        output: String,
        error: String,
        time: TimeStamp,
    },
    Chat {
        role: Role,
        content: String,
    },
}

/// Shell助手响应
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ShellAssistResponse {
    pub name: String,
    pub avatar: String,
    pub recommend_command: String,
    pub content: String,
    pub chat_scoped_char_memory: HashMap<String, serde_json::Value>,
}

/// 消息编辑参数
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct MessageEditArgs {
    pub index: usize,
    pub original: ChatLogEntry,
    pub edited: ChatReply,
    pub chat_log: Vec<ChatLogEntry>,
    pub extension: Option<HashMap<String, serde_json::Value>>,
}

/// 消息删除参数
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct MessageDeleteArgs {
    pub index: usize,
    pub chat_log: Vec<ChatLogEntry>,
    pub chat_entry: ChatLogEntry,
    pub extension: Option<HashMap<String, serde_json::Value>>,
}

/// 角色更新请求
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct UpdateCharacterRequest {
    pub name: Option<String>,
    pub description: Option<String>,
    pub avatar_url: Option<String>,
    pub personality: Option<String>,
    pub background: Option<String>,
    pub greeting: Option<String>,
    pub example_conversations: Option<Vec<String>>,
    pub tags: Option<Vec<String>>,
    pub status: Option<Status>,
    pub metadata: Option<HashMap<String, serde_json::Value>>,
}

/// 角色搜索请求
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SearchCharacterRequest {
    pub query: Option<String>,
    pub tags: Option<Vec<String>>,
    pub creator_id: Option<String>,
    pub status: Option<Status>,
    pub page: Option<u32>,
    pub per_page: Option<u32>,
}

/// 角色列表响应
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct CharacterListResponse {
    pub characters: Vec<Character>,
    pub pagination: crate::Pagination,
}

/// 角色统计信息
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct CharacterStats {
    pub total_characters: u64,
    pub active_characters: u64,
    pub total_conversations: u64,
    pub total_messages: u64,
}

/// 角色API响应类型别名
pub type CharacterResponse = ApiResponse<Character>;
pub type CharacterListApiResponse = ApiResponse<CharacterListResponse>;
pub type CharacterStatsResponse = ApiResponse<CharacterStats>;

/// 角色信息接口 (对应 interfaces.info)
#[async_trait]
pub trait CharInfoInterface {
    async fn update_info(&self, locales: Vec<Locale>) -> crate::Result<Info>;
}

/// 角色配置接口 (对应 interfaces.config)
#[async_trait]
pub trait CharConfigInterface {
    async fn get_data(&self) -> crate::Result<serde_json::Value>;
    async fn set_data(&self, data: serde_json::Value) -> crate::Result<()>;
}

/// 角色聊天接口 (对应 interfaces.chat)
#[async_trait]
pub trait CharChatInterface {
    async fn get_greeting(&self, arg: ChatReplyRequest, index: usize) -> crate::Result<Option<ChatReply>>;
    async fn get_group_greeting(&self, arg: ChatReplyRequest, index: usize) -> crate::Result<Option<ChatReply>>;
    async fn get_prompt(&self, arg: ChatReplyRequest, prompt_struct: PromptStruct, detail_level: u32) -> crate::Result<SinglePartPrompt>;
    async fn get_prompt_for_other(&self, arg: ChatReplyRequest, prompt_struct: PromptStruct, detail_level: u32) -> crate::Result<SinglePartPrompt>;
    async fn get_reply(&self, arg: ChatReplyRequest) -> crate::Result<Option<ChatReply>>;
    async fn get_reply_frequency(&self, arg: ChatReplyRequest) -> crate::Result<f64>;
    async fn message_edit(&self, arg: MessageEditArgs) -> crate::Result<ChatReply>;
    async fn message_editing(&self, arg: MessageEditArgs) -> crate::Result<()>;
    async fn message_delete(&self, arg: MessageDeleteArgs) -> crate::Result<()>;
}

/// Discord配置模板
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct DiscordBotConfig {
    pub token: String,
    pub intents: Vec<String>,
    pub partials: Vec<String>,
    pub settings: HashMap<String, serde_json::Value>,
}

/// 角色Discord接口 (对应 interfaces.discord)
#[async_trait]
pub trait CharDiscordInterface {
    async fn get_intents(&self) -> crate::Result<Vec<String>>;
    async fn get_partials(&self) -> crate::Result<Vec<String>>;
    async fn once_client_ready(&self, config: DiscordBotConfig) -> crate::Result<()>;
    async fn get_bot_config_template(&self) -> crate::Result<DiscordBotConfig>;
}

/// Telegram配置模板
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct TelegramBotConfig {
    pub token: String,
    pub settings: HashMap<String, serde_json::Value>,
}

/// 角色Telegram接口 (对应 interfaces.telegram)
#[async_trait]
pub trait CharTelegramInterface {
    async fn bot_setup(&self, config: TelegramBotConfig) -> crate::Result<()>;
    async fn get_bot_config_template(&self) -> crate::Result<TelegramBotConfig>;
}

/// 角色Shell助手接口 (对应 interfaces.shellassist)
#[async_trait]
pub trait CharShellAssistInterface {
    async fn assist(&self, data: ShellAssistData) -> crate::Result<ShellAssistResponse>;
}

/// 角色API主接口 (对应 charAPI_t)
#[async_trait]
pub trait CharacterApi {
    /// 获取角色信息
    fn get_info(&self) -> &Info;

    /// 角色安装时调用，失败时删除角色文件夹下所有文件
    async fn init(&mut self, stat: CharInit) -> crate::Result<()>;

    /// 每次角色启动时调用，失败时弹出消息
    async fn load(&mut self, stat: CharInit) -> crate::Result<()>;

    /// 每次角色卸载时调用
    async fn unload(&mut self, reason: String) -> crate::Result<()>;

    /// 角色卸载时调用
    async fn uninstall(&mut self, reason: String, from: String) -> crate::Result<()>;

    /// 获取信息接口
    fn info_interface(&self) -> Option<&dyn CharInfoInterface>;

    /// 获取配置接口
    fn config_interface(&self) -> Option<&dyn CharConfigInterface>;

    /// 获取聊天接口
    fn chat_interface(&self) -> Option<&dyn CharChatInterface>;

    /// 获取Discord接口
    fn discord_interface(&self) -> Option<&dyn CharDiscordInterface>;

    /// 获取Telegram接口
    fn telegram_interface(&self) -> Option<&dyn CharTelegramInterface>;

    /// 获取Shell助手接口
    fn shell_assist_interface(&self) -> Option<&dyn CharShellAssistInterface>;
}
