//! 系统通知工具
//! 对应原文件: src/scripts/notify.mjs

use notify_rust::{Notification, Timeout};
use anyhow::Result;
use std::path::Path;
use tracing::{debug, warn};

/// 通知选项
#[derive(Debug, <PERSON><PERSON>, Default)]
pub struct NotifyOptions {
    pub icon: Option<String>,
    pub timeout: Option<u32>,
    pub urgency: Option<String>,
    pub sound: Option<String>,
}

/// 检查是否在Docker环境中
pub fn in_docker() -> bool {
    std::env::var("DOCKER_CONTAINER").is_ok() ||
    Path::new("/.dockerenv").exists()
}

/// 检查是否在Termux环境中
pub fn in_termux() -> bool {
    std::env::var("TERMUX_VERSION").is_ok()
}

/// 发送系统通知 (对应 notify 函数)
pub async fn notify(title: &str, message: &str, options: Option<NotifyOptions>) -> Result<()> {
    debug!("Sending notification: {} - {}", title, message);

    // 在Docker或Termux环境中跳过通知
    if in_docker() || in_termux() {
        debug!("Skipping notification in Docker/Termux environment");
        return Ok(());
    }

    let options = options.unwrap_or_default();

    // Windows平台使用PowerShell显示消息框
    if cfg!(target_os = "windows") {
        let escaped_title = title.replace('"', "'");
        let escaped_message = message.replace('"', "'");

        let command = format!(
            r#"Add-Type -AssemblyName System.Windows.Forms; [System.Windows.Forms.MessageBox]::Show("{}", "{}", 0, [System.Windows.Forms.MessageBoxIcon]::Information)"#,
            escaped_message, escaped_title
        );

        match crate::exec::exec_sync(&format!("powershell.exe -Command \"{}\"", command)) {
            Ok(result) => {
                if result.status != 0 {
                    warn!("PowerShell notification failed: {}", result.stderr);
                }
            }
            Err(e) => {
                warn!("Failed to execute PowerShell notification: {}", e);
            }
        }

        return Ok(());
    }

    // 其他平台使用notify-rust
    let mut notification = Notification::new();
    notification.summary(title).body(message);

    // 设置图标
    if let Some(icon_path) = options.icon {
        if Path::new(&icon_path).exists() {
            notification.icon(&icon_path);
        }
    }

    // 设置超时
    if let Some(timeout_ms) = options.timeout {
        notification.timeout(Timeout::Milliseconds(timeout_ms));
    } else {
        notification.timeout(Timeout::Milliseconds(6000));
    }

    // 发送通知
    match notification.show() {
        Ok(_) => {
            debug!("Notification sent successfully");
            Ok(())
        }
        Err(e) => {
            warn!("Failed to send notification: {}", e);
            Err(anyhow::anyhow!("Failed to send notification: {}", e))
        }
    }
}

/// 发送简单通知
pub async fn notify_simple(title: &str, message: &str) -> Result<()> {
    notify(title, message, None).await
}

/// 发送带图标的通知
pub async fn notify_with_icon(title: &str, message: &str, icon_path: &str) -> Result<()> {
    let options = NotifyOptions {
        icon: Some(icon_path.to_string()),
        ..Default::default()
    };
    notify(title, message, Some(options)).await
}

/// 发送成功通知
pub async fn notify_success(title: &str, message: &str) -> Result<()> {
    notify(&format!("✓ {}", title), message, None).await
}

/// 发送错误通知
pub async fn notify_error(title: &str, message: &str) -> Result<()> {
    let options = NotifyOptions {
        timeout: Some(10000),
        ..Default::default()
    };
    notify(&format!("✗ {}", title), message, Some(options)).await
}
