//! Fount服务器核心库
//! 
//! 这个crate包含了Fount项目的服务器核心实现，
//! 对应原JavaScript/TypeScript项目的src/server/目录。

// 重新导出依赖
pub use fount_types::*;
pub use fount_utils::*;

// 服务器核心模块
pub mod base;
pub mod config;
pub mod server;
pub mod auth;
pub mod endpoints;
pub mod events;
pub mod ipc_server;
pub mod jobs;
pub mod on_shutdown;
pub mod parts_loader;
pub mod setting_loader;
pub mod timers;

// 管理器模块
pub mod managers;

// 重新导出主要组件
pub use base::*;
pub use config::*;
pub use server::*;
pub use auth::*;
pub use endpoints::*;
pub use events::*;
pub use ipc_server::*;
pub use jobs::*;
pub use on_shutdown::*;
pub use parts_loader::*;
pub use setting_loader::*;
pub use timers::*;
pub use managers::*;
