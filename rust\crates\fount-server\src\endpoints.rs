//! API端点处理器
//! 对应原文件: src/server/endpoints.mjs

use axum::{
    Router,
    routing::{get, post},
    extract::{Path, Query, State, Request},
    http::{StatusCode, HeaderMap},
    J<PERSON>,
    response::{<PERSON><PERSON> as ResponseJson, Response},
    middleware,
};
use serde::{Deserialize, Serialize};
use std::sync::Arc;
use uuid::Uuid;
use anyhow::Result;
use tracing::{info, warn, error, debug};
use tower_http::cors::CorsLayer;

use crate::{
    config::ServerConfig,
    auth::{AuthService, hash_password, verify_password, get_user_by_username},
    server::AppState,
};
use fount_utils::{console, i18n::t};
use fount_types::{
    Character, CreateCharacterRequest, UpdateCharacterRequest,
    CharacterListResponse, ApiResponse, LoginRequest, LoginResponse,
    AiChatRequest, AiChatResponse, User, PermissionLevel, Status,
};

/// 测试错误端点 (对应 /api/test/error)
pub async fn test_error() -> Result<(), StatusCode> {
    Err(StatusCode::INTERNAL_SERVER_ERROR)
}

/// 测试异步错误端点 (对应 /api/test/async_error)
pub async fn test_async_error() -> Result<(), StatusCode> {
    tokio::time::sleep(tokio::time::Duration::from_millis(1)).await;
    Err(StatusCode::INTERNAL_SERVER_ERROR)
}

/// Ping端点 (对应 /api/ping)
pub async fn ping(headers: HeaderMap) -> ResponseJson<serde_json::Value> {
    // 简化的本地IP检测
    let is_local = headers.get("x-forwarded-for")
        .and_then(|h| h.to_str().ok())
        .map(|ip| ip.starts_with("127.") || ip.starts_with("192.168.") || ip == "::1")
        .unwrap_or(true);

    ResponseJson(serde_json::json!({
        "message": "pong",
        "client_name": "fount",
        "is_local_ip": is_local
    }))
}

/// 获取本地化数据端点 (对应 /api/getlocaledata)
pub async fn get_locale_data(headers: HeaderMap) -> ResponseJson<serde_json::Value> {
    // 简化的语言偏好处理
    let preferred_languages = headers.get("accept-language")
        .and_then(|h| h.to_str().ok())
        .map(|lang| lang.split(',').map(|l| l.trim().split(';').next().unwrap_or("").to_string()).collect::<Vec<_>>())
        .unwrap_or_else(|| vec!["en-UK".to_string()]);

    // 返回简化的本地化数据
    ResponseJson(serde_json::json!({
        "locale": preferred_languages.first().unwrap_or(&"en-UK".to_string()),
        "data": {}
    }))
}

/// 用户登录 (对应 /api/login)
pub async fn login(
    State(state): State<AppState>,
    Json(payload): Json<LoginRequest>,
) -> Result<ResponseJson<serde_json::Value>, StatusCode> {
    // 简化的登录逻辑
    if payload.username == "admin" && payload.password == "password" {
        let auth_service = AuthService::new().map_err(|_| StatusCode::INTERNAL_SERVER_ERROR)?;
        let token = auth_service.generate_access_token(&payload.username, "admin")
            .map_err(|_| StatusCode::INTERNAL_SERVER_ERROR)?;

        Ok(ResponseJson(serde_json::json!({
            "status": 200,
            "success": true,
            "accessToken": token,
            "message": "Login successful"
        })))
    } else {
        Ok(ResponseJson(serde_json::json!({
            "status": 401,
            "success": false,
            "message": "Invalid credentials"
        })))
    }
}

/// 用户登出 (对应 /api/logout)
pub async fn logout() -> ResponseJson<serde_json::Value> {
    ResponseJson(serde_json::json!({
        "success": true,
        "message": "Logout successful"
    }))
}

/// 身份验证端点 (对应 /api/authenticate)
pub async fn authenticate() -> ResponseJson<serde_json::Value> {
    ResponseJson(serde_json::json!({
        "message": "Authenticated"
    }))
}

/// 获取当前用户信息端点 (对应 /api/whoami)
pub async fn whoami() -> ResponseJson<serde_json::Value> {
    ResponseJson(serde_json::json!({
        "username": "admin"
    }))
}

/// 健康检查端点 (对应 /heartbeat)
pub async fn health_check() -> ResponseJson<serde_json::Value> {
    ResponseJson(serde_json::json!({
        "status": "ok",
        "timestamp": chrono::Utc::now()
    }))
}

/// 创建API路由 (对应原文件的 registerEndpoints)
pub fn register_endpoints() -> Router<AppState> {
    Router::new()
        // 测试端点
        .route("/test/error", get(test_error))
        .route("/test/async_error", get(test_async_error))

        // 基础端点
        .route("/ping", get(ping))
        .route("/getlocaledata", get(get_locale_data))
        .route("/heartbeat", get(health_check))

        // 认证端点
        .route("/login", post(login))
        .route("/logout", post(logout))
        .route("/authenticate", post(authenticate))
        .route("/whoami", get(whoami))

        // 添加CORS支持
        .layer(CorsLayer::permissive())
}
