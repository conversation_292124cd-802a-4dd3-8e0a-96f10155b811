//! JSON文件加载器
//! 对应原文件: src/scripts/json_loader.mjs

use serde::{Deserialize, Serialize};
use std::path::Path;
use anyhow::Result;
use tracing::{error, warn};

/// 加载JSON文件 (对应 loadJsonFile)
pub fn load_json_file<T>(filename: impl AsRef<Path>) -> Result<T>
where
    T: for<'de> Deserialize<'de>,
{
    let path = filename.as_ref();
    let content = std::fs::read_to_string(path)
        .map_err(|e| anyhow::anyhow!("Failed to read file {}: {}", path.display(), e))?;

    let data = serde_json::from_str(&content)
        .map_err(|e| anyhow::anyhow!("Failed to parse JSON from {}: {}", path.display(), e))?;

    Ok(data)
}

/// 如果文件存在则加载JSON文件，否则返回默认值 (对应 loadJsonFileIfExists)
pub fn load_json_file_if_exists<T>(filename: impl AsRef<Path>, default_value: T) -> T
where
    T: for<'de> Deserialize<'de>,
{
    let path = filename.as_ref();

    if !path.exists() {
        return default_value;
    }

    match load_json_file(path) {
        Ok(data) => data,
        Err(e) => {
            warn!("Failed to load JSON file {}: {}, using default value", path.display(), e);
            default_value
        }
    }
}

/// 保存JSON文件 (对应 saveJsonFile)
pub fn save_json_file<T>(filename: impl AsRef<Path>, data: &T) -> Result<()>
where
    T: Serialize,
{
    let path = filename.as_ref();

    // 确保父目录存在
    if let Some(parent) = path.parent() {
        std::fs::create_dir_all(parent)
            .map_err(|e| anyhow::anyhow!("Failed to create directory {}: {}", parent.display(), e))?;
    }

    let content = serde_json::to_string_pretty(data)
        .map_err(|e| anyhow::anyhow!("Failed to serialize data: {}", e))?;

    std::fs::write(path, content)
        .map_err(|e| {
            error!("Error saving JSON file: {} - {}", path.display(), e);
            anyhow::anyhow!("Failed to write file {}: {}", path.display(), e)
        })?;

    Ok(())
}

/// 异步加载JSON文件
pub async fn load_json_file_async<T>(filename: impl AsRef<Path>) -> Result<T>
where
    T: for<'de> Deserialize<'de>,
{
    let path = filename.as_ref().to_path_buf();

    tokio::task::spawn_blocking(move || load_json_file(path))
        .await
        .map_err(|e| anyhow::anyhow!("Task join error: {}", e))?
}

/// 异步保存JSON文件
pub async fn save_json_file_async<T>(filename: impl AsRef<Path>, data: T) -> Result<()>
where
    T: Serialize + Send + 'static,
{
    let path = filename.as_ref().to_path_buf();

    tokio::task::spawn_blocking(move || save_json_file(path, &data))
        .await
        .map_err(|e| anyhow::anyhow!("Task join error: {}", e))?
}

/// JSON文件操作的便捷宏
#[macro_export]
macro_rules! load_json {
    ($path:expr) => {
        $crate::json_loader::load_json_file($path)
    };
    ($path:expr, $default:expr) => {
        $crate::json_loader::load_json_file_if_exists($path, $default)
    };
}

#[macro_export]
macro_rules! save_json {
    ($path:expr, $data:expr) => {
        $crate::json_loader::save_json_file($path, &$data)
    };
}
