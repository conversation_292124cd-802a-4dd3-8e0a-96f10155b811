//! 事件系统
//! 对应原文件: src/server/events.mjs

use tokio::sync::broadcast;
use serde::{Deserialize, Serialize};
use uuid::Uuid;
use chrono::{DateTime, Utc};
use std::collections::HashMap;
use anyhow::Result;

/// 事件类型
#[derive(Debug, Clone, Serialize, Deserialize)]
#[serde(tag = "type")]
pub enum Event {
    UserConnected { user_id: String, session_id: Uuid },
    UserDisconnected { user_id: String, session_id: Uuid },
    MessageSent { from: String, to: String, content: String },
    CharacterCreated { character_id: Uuid, creator_id: String },
    CharacterUpdated { character_id: Uuid, updater_id: String },
    SystemNotification { message: String, level: String },
}

/// 事件数据
#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize)]
pub struct EventData {
    pub id: Uuid,
    pub event: Event,
    pub timestamp: DateTime<Utc>,
    pub metadata: HashMap<String, serde_json::Value>,
}

impl EventData {
    pub fn new(event: Event) -> Self {
        Self {
            id: Uuid::new_v4(),
            event,
            timestamp: Utc::now(),
            metadata: HashMap::new(),
        }
    }
}

/// 事件管理器
pub struct EventManager {
    sender: broadcast::Sender<EventData>,
    _receiver: broadcast::Receiver<EventData>,
}

impl EventManager {
    /// 创建新的事件管理器
    pub fn new() -> Self {
        let (sender, receiver) = broadcast::channel(1000);
        Self {
            sender,
            _receiver: receiver,
        }
    }
    
    /// 发送事件
    pub fn emit(&self, event: Event) -> Result<()> {
        let event_data = EventData::new(event);
        self.sender.send(event_data)?;
        Ok(())
    }
    
    /// 订阅事件
    pub fn subscribe(&self) -> broadcast::Receiver<EventData> {
        self.sender.subscribe()
    }
    
    /// 获取订阅者数量
    pub fn subscriber_count(&self) -> usize {
        self.sender.receiver_count()
    }
}

impl Default for EventManager {
    fn default() -> Self {
        Self::new()
    }
}
