//! 增强控制台工具
//! 对应原文件: src/scripts/console.mjs

use std::io::{self, Write};
use std::sync::{Arc, Mutex};
use crossterm::{
    cursor,
    terminal::{self, ClearType},
    style::{Color, SetForegroundColor, ResetColor},
    execute, ExecutableCommand,
};
use tracing::{debug, error, info, warn};

/// 增强控制台 (对应 myConsole)
#[derive(Debug, Clone)]
pub struct Console {
    logged_fresh_line: Arc<Mutex<Option<String>>>,
}

impl Console {
    /// 创建新的控制台实例
    pub fn new() -> Self {
        Self {
            logged_fresh_line: Arc::new(Mutex::new(None)),
        }
    }

    /// 普通日志输出 (对应 log)
    pub fn log(&self, message: &str) {
        if let Ok(mut logged) = self.logged_fresh_line.lock() {
            *logged = None;
        }
        println!("{}", message);
        info!("{}", message);
    }

    /// 格式化日志输出
    pub fn logf(&self, args: std::fmt::Arguments) {
        if let Ok(mut logged) = self.logged_fresh_line.lock() {
            *logged = None;
        }
        println!("{}", args);
        info!("{}", args);
    }

    /// 目录输出 (对应 dir)
    pub fn dir<T: std::fmt::Debug>(&self, value: &T) {
        if let Ok(mut logged) = self.logged_fresh_line.lock() {
            *logged = None;
        }
        println!("{:#?}", value);
        debug!("{:#?}", value);
    }

    /// 错误输出 (对应 error)
    pub fn error(&self, message: &str) {
        if let Ok(mut logged) = self.logged_fresh_line.lock() {
            *logged = None;
        }
        eprintln!("{}", message);
        error!("{}", message);
    }

    /// 刷新行输出 (对应 freshLine)
    pub fn fresh_line(&self, id: &str, message: &str) -> io::Result<()> {
        let should_clear = {
            if let Ok(logged) = self.logged_fresh_line.lock() {
                logged.as_ref().map_or(false, |logged_id| logged_id == id)
            } else {
                false
            }
        };

        if should_clear {
            // 清除当前行并重新输出
            io::stdout()
                .execute(cursor::MoveToPreviousLine(1))?
                .execute(terminal::Clear(ClearType::CurrentLine))?;
        }

        if let Ok(mut logged) = self.logged_fresh_line.lock() {
            *logged = Some(id.to_string());
        }

        println!("{}", message);
        io::stdout().flush()?;

        Ok(())
    }

    /// 警告输出
    pub fn warn(&self, message: &str) {
        if let Ok(mut logged) = self.logged_fresh_line.lock() {
            *logged = None;
        }
        eprintln!("Warning: {}", message);
        warn!("{}", message);
    }

    /// 调试输出
    pub fn debug(&self, message: &str) {
        if let Ok(mut logged) = self.logged_fresh_line.lock() {
            *logged = None;
        }
        println!("Debug: {}", message);
        debug!("{}", message);
    }

    /// 清除控制台
    pub fn clear(&self) -> io::Result<()> {
        io::stdout().execute(terminal::Clear(ClearType::All))?;
        io::stdout().execute(cursor::MoveTo(0, 0))?;
        Ok(())
    }

    /// 彩色输出
    pub fn print_colored(&self, message: &str, color: Color) -> io::Result<()> {
        let mut stdout = io::stdout();
        execute!(stdout, SetForegroundColor(color))?;
        write!(stdout, "{}", message)?;
        execute!(stdout, ResetColor)?;
        stdout.flush()?;
        Ok(())
    }

    /// 成功消息
    pub fn success(&self, message: &str) {
        let _ = self.print_colored(&format!("✓ {}\n", message), Color::Green);
        info!("✓ {}", message);
    }

    /// 信息消息
    pub fn info(&self, message: &str) {
        let _ = self.print_colored(&format!("ℹ {}\n", message), Color::Blue);
        info!("ℹ {}", message);
    }
}

impl Default for Console {
    fn default() -> Self {
        Self::new()
    }
}

/// 全局控制台实例
static GLOBAL_CONSOLE: std::sync::OnceLock<Console> = std::sync::OnceLock::new();

/// 获取全局控制台实例
pub fn console() -> &'static Console {
    GLOBAL_CONSOLE.get_or_init(Console::new)
}

/// 便捷宏
#[macro_export]
macro_rules! console_log {
    ($($arg:tt)*) => {
        $crate::console::console().logf(format_args!($($arg)*))
    };
}

#[macro_export]
macro_rules! console_error {
    ($($arg:tt)*) => {
        $crate::console::console().error(&format!($($arg)*))
    };
}

#[macro_export]
macro_rules! console_warn {
    ($($arg:tt)*) => {
        $crate::console::console().warn(&format!($($arg)*))
    };
}

#[macro_export]
macro_rules! console_debug {
    ($($arg:tt)*) => {
        $crate::console::console().debug(&format!($($arg)*))
    };
}

#[macro_export]
macro_rules! console_fresh_line {
    ($id:expr, $($arg:tt)*) => {
        let _ = $crate::console::console().fresh_line($id, &format!($($arg)*));
    };
}
