//! 导入处理器API类型定义
//! 对应原文件: src/decl/importHandlerAPI.ts

use serde::{Deserialize, Serialize};
use std::collections::HashMap;
use uuid::Uuid;
use chrono::{DateTime, Utc};
use async_trait::async_trait;
use crate::{ApiResponse, Status, Info, Locale};

/// 导入处理器信息接口
#[async_trait]
pub trait ImportHandlerInfoInterface {
    async fn update_info(&self, locales: Vec<Locale>) -> crate::Result<Info>;
}

/// 导入处理器配置接口
#[async_trait]
pub trait ImportHandlerConfigInterface {
    async fn get_data(&self) -> crate::Result<serde_json::Value>;
    async fn set_data(&self, data: serde_json::Value) -> crate::Result<()>;
}

/// 导入处理器导入接口
#[async_trait]
pub trait ImportHandlerImportInterface {
    async fn can_handle(&self, file_path: String, mime_type: String) -> crate::Result<bool>;
    async fn import_file(&self, file_path: String, options: serde_json::Value) -> crate::Result<serde_json::Value>;
}

/// 导入处理器API主接口
#[async_trait]
pub trait ImportHandlerApi {
    /// 获取导入处理器信息
    fn get_info(&self) -> &Info;

    /// 导入处理器初始化
    async fn init(&mut self) -> crate::Result<()>;

    /// 导入处理器加载
    async fn load(&mut self) -> crate::Result<()>;

    /// 导入处理器卸载
    async fn unload(&mut self) -> crate::Result<()>;

    /// 导入处理器卸载安装
    async fn uninstall(&mut self) -> crate::Result<()>;

    /// 获取信息接口
    fn info_interface(&self) -> Option<&dyn ImportHandlerInfoInterface>;

    /// 获取配置接口
    fn config_interface(&self) -> Option<&dyn ImportHandlerConfigInterface>;

    /// 获取导入接口
    fn import_interface(&self) -> Option<&dyn ImportHandlerImportInterface>;
}

/// 导入处理器
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ImportHandler {
    pub id: Uuid,
    pub name: String,
    pub description: String,
    pub supported_formats: Vec<String>,
    pub status: Status,
    pub created_at: DateTime<Utc>,
    pub updated_at: DateTime<Utc>,
    pub metadata: HashMap<String, serde_json::Value>,
}

/// 导入请求
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ImportRequest {
    pub handler_id: Uuid,
    pub file_data: Vec<u8>,
    pub file_name: String,
    pub options: serde_json::Value,
}

/// 导入响应
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ImportResponse {
    pub success: bool,
    pub imported_items: Vec<serde_json::Value>,
    pub errors: Vec<String>,
}

/// 导入处理器响应类型别名
pub type ImportHandlerResponse = ApiResponse<ImportHandler>;
pub type ImportHandlerListResponse = ApiResponse<Vec<ImportHandler>>;
pub type ImportApiResponse = ApiResponse<ImportResponse>;
