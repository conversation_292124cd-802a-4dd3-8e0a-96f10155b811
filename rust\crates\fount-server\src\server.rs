//! 主Web服务器
//! 对应原文件: src/server/server.mjs

use axum::{
    Router,
    routing::{get, post},
    extract::{State, Request},
    response::{Html, Json},
    http::{StatusCode, Method},
    middleware::{self, Next},
};
use tower::ServiceBuilder;
use tower_http::{
    cors::{CorsLayer, Any},
    trace::TraceLayer,
    services::ServeDir,
    compression::CompressionLayer,
};
use std::sync::Arc;
use std::net::SocketAddr;
use anyhow::Result;
use tracing::{info, error, warn, debug};
use tokio::signal;
use sentry_tower::NewSentryLayer;

use crate::{
    config::{ServerConfig, load_config, get_config_clone},
    base::{init_base, get_uptime_seconds, set_start_time},
    auth::init_auth,
    endpoints::register_endpoints,
    ipc::IpcManager,
};
use fount_utils::{console, notify, tray, i18n::t};

/// 服务器状态 (对应原文件的全局变量)
#[derive(Clone)]
pub struct AppState {
    pub config: Arc<ServerConfig>,
}

/// 全局服务器状态
static GLOBAL_STATE: std::sync::OnceLock<AppState> = std::sync::OnceLock::new();

/// 获取全局状态
pub fn get_global_state() -> Option<&'static AppState> {
    GLOBAL_STATE.get()
}

/// 设置窗口标题 (对应 setWindowTitle)
fn set_window_title(title: &str) {
    print!("\x1b]2;{}\x1b\\", title);
}

/// 设置默认配置 (对应 setDefaultStuff)
pub fn set_default_stuff() {
    set_window_title("fount");
}

/// 请求日志中间件 (对应原文件的请求日志逻辑)
async fn request_logger(req: Request, next: Next) -> axum::response::Response {
    let method = req.method().clone();
    let uri = req.uri().clone();

    // 跳过心跳和Sentry隧道的日志
    if !uri.path().ends_with("/heartbeat") && !uri.path().ends_with("/api/sentrytunnel") {
        let method_padded = format!("{:<8}", method.as_str());
        console().log(&format!("Request received: {} {}", method_padded, uri));
    }

    next.run(req).await
}

/// Fount服务器主结构
pub struct FountServer {
    config: ServerConfig,
    app: Router,
}

impl FountServer {
    /// 创建新的服务器实例
    pub async fn new(config: ServerConfig) -> Result<Self> {
        let app = create_app(&config).await?;

        Ok(Self {
            config,
            app,
        })
    }

    /// 启动服务器
    pub async fn start(self) -> Result<()> {
        let addr: SocketAddr = format!("{}:{}", self.config.host, self.config.port).parse()?;
        let listener = tokio::net::TcpListener::bind(&addr).await?;

        info!("Server listening on {}", addr);
        console().log(&format!("Server URL: {}", self.config.get_host_url()));

        axum::serve(listener, self.app).await?;
        Ok(())
    }
}

/// 创建主应用路由 (对应原文件的 app, mainRouter, PartsRouter, FinalRouter)
pub fn create_app(config: &ServerConfig) -> Router {
    let state = AppState {
        config: Arc::new(config.clone()),
    };

    let cors = CorsLayer::new()
        .allow_methods([Method::GET, Method::POST, Method::PUT, Method::DELETE])
        .allow_headers(Any)
        .allow_origin(Any);

    Router::new()
        // 主路由 (对应 mainRouter)
        .nest("/api", register_endpoints())
        // 静态文件服务 (对应 express.static)
        .nest_service("/", ServeDir::new(&config.static_dir))
        // 特殊路由处理 (对应原文件的 apple-touch-icon 处理)
        .route("/apple-touch-icon.png", get(apple_touch_icon))
        .route("/apple-touch-icon-precomposed.png", get(apple_touch_icon))
        // 404处理 (对应 FinalRouter)
        .fallback(handle_404)
        .layer(
            ServiceBuilder::new()
                .layer(NewSentryLayer::new_from_top())
                .layer(TraceLayer::new_for_http())
                .layer(CompressionLayer::new())
                .layer(cors)
                .layer(middleware::from_fn(request_logger))
        )
        .with_state(state)
}

/// Apple touch icon处理 (对应原文件的特殊路由处理)
async fn apple_touch_icon() -> Result<axum::response::Response, StatusCode> {
    match tokio::fs::read("src/public/favicon.png").await {
        Ok(content) => {
            Ok(axum::response::Response::builder()
                .header("content-type", "image/png")
                .body(axum::body::Body::from(content))
                .unwrap())
        }
        Err(_) => Err(StatusCode::NOT_FOUND),
    }
}

/// 404处理 (对应 FinalRouter 的404处理)
async fn handle_404() -> (StatusCode, Html<String>) {
    let html = match tokio::fs::read_to_string("src/public/404.html").await {
        Ok(content) => content,
        Err(_) => "<h1>404 Not Found</h1>".to_string(),
    };

    (StatusCode::NOT_FOUND, Html(html))
}

/// 健康检查端点 (对应 /heartbeat)
pub async fn health_check() -> Json<serde_json::Value> {
    Json(serde_json::json!({
        "status": "ok",
        "timestamp": chrono::Utc::now(),
        "uptime": get_uptime_seconds()
    }))
}

/// 初始化服务器 (对应原文件的 init 函数)
pub async fn init() -> Result<bool> {
    console().fresh_line("server start", &t("fountConsole.server.start").await)?;

    // 初始化基础环境
    init_base()?;

    // 设置全局错误处理
    std::panic::set_hook(Box::new(|panic_info| {
        error!("Panic occurred: {}", panic_info);
        sentry::capture_message(&format!("Panic: {}", panic_info), sentry::Level::Fatal);
    }));

    // 加载配置 (对应 get_config)
    let config = load_config()?;

    // 初始化认证 (对应 initAuth)
    init_auth().await?;

    // 启动IPC服务器 (对应 IPCManager)
    let ipc_manager = IpcManager::new();
    if !ipc_manager.start_server().await? {
        return Ok(false);
    }

    // 创建全局状态
    let state = AppState {
        config: Arc::new(config.clone()),
    };

    GLOBAL_STATE.set(state).map_err(|_| {
        anyhow::anyhow!("Failed to set global state")
    })?;

    console().fresh_line("server start", &t("fountConsole.server.starting").await)?;

    // 创建应用
    let app = create_app(&config);

    // 确定监听地址
    let addr: SocketAddr = format!("{}:{}", config.host, config.port).parse()?;

    // 启动服务器
    let listener = tokio::net::TcpListener::bind(addr).await?;

    // 显示URL (对应原文件的URL显示逻辑)
    let host_url = config.get_host_url();

    info!("Server listening on {}", host_url);
    console().log(&format!("Server URL: {}", host_url));

    // 启动后台任务
    tokio::spawn(async move {
        // 创建系统托盘 (对应 createTray)
        if let Err(e) = tray::create_tray() {
            warn!("Failed to create system tray: {}", e);
        }

        // 设置默认配置
        set_default_stuff();

        // 显示启动完成信息
        let startup_time = get_uptime_seconds();

        console().fresh_line("server start", "fo-fo-fount!")?;
        console().log(&format!("Server started in {:.3}s", startup_time));

        // 发送启动通知
        if let Err(e) = notify::notify_success("Fount", "Server started successfully").await {
            debug!("Failed to send startup notification: {}", e);
        }

        Ok::<(), anyhow::Error>(())
    });

    // 启动服务器
    tokio::spawn(async move {
        if let Err(e) = axum::serve(listener, app).await {
            error!("Server error: {}", e);
        }
    });

    console().fresh_line("server start", &t("fountConsole.server.ready").await)?;

    Ok(true)
}

/// 优雅关闭处理 (对应 on_shutdown)
pub async fn shutdown_handler() {
    let ctrl_c = async {
        signal::ctrl_c()
            .await
            .expect("failed to install Ctrl+C handler");
    };

    #[cfg(unix)]
    let terminate = async {
        signal::unix::signal(signal::unix::SignalKind::terminate())
            .expect("failed to install signal handler")
            .recv()
            .await;
    };

    #[cfg(not(unix))]
    let terminate = std::future::pending::<()>();

    tokio::select! {
        _ = ctrl_c => {},
        _ = terminate => {},
    }

    info!("Shutdown signal received, starting graceful shutdown");

    // 销毁系统托盘
    tray::destroy_tray();

    // 发送关闭通知
    if let Err(e) = notify::notify_simple("Fount", "Server shutting down").await {
        debug!("Failed to send shutdown notification: {}", e);
    }
}
