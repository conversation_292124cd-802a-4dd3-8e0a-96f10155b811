//! 系统托盘工具
//! 对应原文件: src/scripts/tray.mjs

use tray_icon::{TrayI<PERSON>, TrayIconBuilder, menu::{Menu, MenuItem}, Icon};
use anyhow::Result;
use std::path::Path;
use tracing::{debug, error, warn};

/// 托盘菜单项ID
#[derive(Debug, Clone, Copy, PartialEq, Eq)]
pub enum TrayMenuId {
    Exit = 0,
    Show = 1,
    Hide = 2,
    Settings = 3,
}

/// 系统托盘管理器 (对应原文件的 createTray 函数功能)
pub struct TrayManager {
    tray: Option<TrayIcon>,
}

impl TrayManager {
    /// 创建新的托盘管理器
    pub fn new() -> Self {
        Self { tray: None }
    }

    /// 创建系统托盘 (对应 createTray)
    pub fn create_tray(&mut self) -> Result<()> {
        debug!("Creating system tray");

        // 根据平台选择图标路径 (对应原文件的平台判断逻辑)
        let icon_path = if cfg!(target_os = "windows") {
            "src/public/favicon.ico"
        } else {
            "imgs/icon.png"
        };

        // 加载图标
        let icon = if Path::new(icon_path).exists() {
            match Icon::from_path(icon_path, None) {
                Ok(icon) => Some(icon),
                Err(e) => {
                    warn!("Failed to load icon from {}: {}", icon_path, e);
                    None
                }
            }
        } else {
            warn!("Icon file not found: {}", icon_path);
            None
        };

        // 创建菜单 (对应原文件的 menu 配置)
        let menu = Menu::new();
        let exit_item = MenuItem::new("Exit", true, None);

        menu.append_items(&[&exit_item])
            .map_err(|e| anyhow::anyhow!("Failed to create menu: {}", e))?;

        // 创建托盘图标
        let mut builder = TrayIconBuilder::new()
            .with_menu(Box::new(menu))
            .with_tooltip("Fount");

        if let Some(icon) = icon {
            builder = builder.with_icon(icon);
        }

        let tray = builder.build()
            .map_err(|e| anyhow::anyhow!("Failed to create tray: {}", e))?;

        self.tray = Some(tray);

        debug!("System tray created successfully");
        Ok(())
    }

    /// 销毁托盘
    pub fn destroy(&mut self) {
        if let Some(_tray) = self.tray.take() {
            debug!("Destroying system tray");
            // tray_icon库会在drop时自动清理
        }
    }

    /// 更新托盘图标
    pub fn update_icon(&mut self, icon_path: impl AsRef<Path>) -> Result<()> {
        if let Some(ref mut tray) = self.tray {
            let icon = Icon::from_path(icon_path, None)
                .map_err(|e| anyhow::anyhow!("Failed to load icon: {}", e))?;

            tray.set_icon(Some(icon))
                .map_err(|e| anyhow::anyhow!("Failed to update icon: {}", e))?;
        }
        Ok(())
    }

    /// 更新托盘提示文本
    pub fn update_tooltip(&mut self, tooltip: &str) -> Result<()> {
        if let Some(ref mut tray) = self.tray {
            tray.set_tooltip(Some(tooltip))
                .map_err(|e| anyhow::anyhow!("Failed to update tooltip: {}", e))?;
        }
        Ok(())
    }
}

impl Default for TrayManager {
    fn default() -> Self {
        Self::new()
    }
}

/// 全局托盘管理器
static GLOBAL_TRAY: std::sync::OnceLock<std::sync::Mutex<TrayManager>> = std::sync::OnceLock::new();

/// 获取全局托盘管理器
pub fn tray_manager() -> &'static std::sync::Mutex<TrayManager> {
    GLOBAL_TRAY.get_or_init(|| std::sync::Mutex::new(TrayManager::new()))
}

/// 创建系统托盘的便捷函数 (对应原文件的 createTray 导出函数)
pub fn create_tray() -> Result<()> {
    if let Ok(mut manager) = tray_manager().lock() {
        manager.create_tray()
    } else {
        Err(anyhow::anyhow!("Failed to lock tray manager"))
    }
}

/// 销毁系统托盘的便捷函数
pub fn destroy_tray() {
    if let Ok(mut manager) = tray_manager().lock() {
        manager.destroy();
    }
}
