//! 用户API类型定义
//! 对应原文件: src/decl/UserAPI.ts

use serde::{Deserialize, Serialize};
use uuid::Uuid;
use chrono::{DateTime, Utc};
use crate::{PermissionLevel, Status};

/// 用户信息
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct User {
    pub id: String,
    pub username: String,
    pub email: String,
    pub display_name: String,
    pub avatar_url: Option<String>,
    pub permission_level: PermissionLevel,
    pub status: Status,
    pub created_at: DateTime<Utc>,
    pub updated_at: DateTime<Utc>,
    pub last_login: Option<DateTime<Utc>>,
}

/// 用户注册请求
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct RegisterRequest {
    pub username: String,
    pub email: String,
    pub password: String,
    pub display_name: String,
}

/// 用户登录请求
#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize)]
pub struct LoginRequest {
    pub username: String,
    pub password: String,
}

/// 登录响应
#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize)]
pub struct LoginResponse {
    pub user: User,
    pub token: String,
    pub expires_at: DateTime<Utc>,
}

/// 用户更新请求
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct UpdateUserRequest {
    pub username: Option<String>,
    pub email: Option<String>,
    pub display_name: Option<String>,
    pub avatar_url: Option<String>,
    pub permission_level: Option<PermissionLevel>,
    pub status: Option<Status>,
}

/// 用户响应类型别名
pub type UserResponse = crate::ApiResponse<User>;
pub type UserListResponse = crate::ApiResponse<Vec<User>>;
pub type LoginApiResponse = crate::ApiResponse<LoginResponse>;
