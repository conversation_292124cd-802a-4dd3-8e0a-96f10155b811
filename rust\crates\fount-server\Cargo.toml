[package]
name = "fount-server"
version = "0.1.0"
edition = "2021"
description = "Fount server implementation"
license = "MIT"
authors = ["Fount Team"]
repository = "https://github.com/steve02081504/fount"

[[bin]]
name = "fount-server"
path = "src/main.rs"

[dependencies]
# 内部依赖
fount-types = { workspace = true }
fount-utils = { workspace = true }

# 核心
tokio = { workspace = true }
serde = { workspace = true }
serde_json = { workspace = true }
anyhow = { workspace = true }
thiserror = { workspace = true }

# Web框架
axum = { version = "0.7.4", features = ["ws", "multipart", "macros"] }
tower = { version = "0.4.13", features = ["full"] }
tower-http = { version = "0.5.0", features = ["fs", "cors", "trace", "compression-gzip", "compression-br"] }
hyper = { version = "1.1.0", features = ["full"] }

# 模板引擎
askama = { version = "0.12.1", features = ["with-axum"] }
askama_axum = "0.4.0"

# HTTP客户端
reqwest = { workspace = true }
reqwest-middleware = { workspace = true }
reqwest-retry = { workspace = true }
reqwest-tracing = { workspace = true }

# 认证和安全
jsonwebtoken = { workspace = true }
bcrypt = { workspace = true }
uuid = { workspace = true }

# 日志和追踪
tracing = { workspace = true }
tracing-subscriber = { workspace = true }
tracing-appender = { workspace = true }

# 时间
chrono = { workspace = true }

# 文件系统
fs_extra = { workspace = true }
walkdir = { workspace = true }

# 配置
config = { workspace = true }
dirs = { workspace = true }

# 数据结构
indexmap = { workspace = true }
dashmap = { workspace = true }

# 异步
async-trait = { workspace = true }
futures = { workspace = true }

# WebSocket
tokio-tungstenite = "0.21.0"

# 文件上传
multer = "3.0.0"

# Cookie处理
cookie = "0.18.0"

# MIME类型
mime = "0.3.17"
mime_guess = "2.0.4"

# 压缩
flate2 = { workspace = true }
zip = { workspace = true }

# 单实例检查
single-instance = "0.3.0"

# 信号处理
signal-hook = "0.3.17"
signal-hook-tokio = { version = "0.3.1", features = ["futures-v0_3"] }

# 性能监控
metrics = "0.22.0"
metrics-exporter-prometheus = "0.13.0"

# 内存映射
memmap2 = "0.9.4"

# 动态库加载
libloading = "0.8.1"

# 进程间通信
interprocess = "1.2.1"

# Sentry
sentry = { workspace = true }
sentry-tower = "0.32.1"
sentry-tracing = "0.32.1"
