//! 认证系统
//! 对应原文件: src/server/auth.mjs

use axum::{
    extract::{Request, State},
    http::{header::AUTHORIZATION, StatusCode},
    middleware::Next,
    response::Response,
    Json,
};
use jsonwebtoken::{decode, encode, Decoding<PERSON>ey, Enco<PERSON><PERSON><PERSON>, Header, Validation, Algorithm};
use serde::{Deserialize, Serialize};
use std::sync::Arc;
use std::collections::HashMap;
use chrono::{DateTime, Utc, Duration};
use anyhow::Result;
use argon2::{Argon2, PasswordHash, PasswordHasher, PasswordVerifier};
use argon2::password_hash::{rand_core::OsRng, SaltString};
use uuid::Uuid;
use tracing::{info, warn, error, debug};

use crate::{
    config::{get_config, save_config, ServerConfig},
    base::get_data_dir,
};
use fount_utils::{json_loader::{load_json_file, save_json_file}, console};
use fount_types::{User, LoginRequest, LoginResponse, PermissionLevel, Status};

/// JWT Claims (对应原文件的JWT payload)
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct Claims {
    pub sub: String,      // username
    pub user_id: String,
    pub jti: String,      // JWT ID
    pub iat: i64,         // issued at
    pub exp: i64,         // expiry
    pub device_id: Option<String>, // for refresh tokens
}

/// 用户认证信息 (对应原文件的用户auth字段)
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct UserAuth {
    pub user_id: String,
    pub password: String,
    pub login_attempts: u32,
    pub locked_until: Option<DateTime<Utc>>,
    pub refresh_tokens: Vec<RefreshTokenInfo>,
}

/// 刷新令牌信息 (对应原文件的refreshTokens数组元素)
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct RefreshTokenInfo {
    pub jti: String,
    pub device_id: String,
    pub expiry: DateTime<Utc>,
    pub ip_address: Option<String>,
    pub user_agent: Option<String>,
    pub last_seen: DateTime<Utc>,
}

/// 撤销的令牌信息 (对应原文件的revokedTokens)
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct RevokedToken {
    pub expiry: DateTime<Utc>,
    pub token_type: String,
    pub revoked_at: DateTime<Utc>,
}

/// 认证数据 (对应原文件的config.data)
#[derive(Debug, Clone, Serialize, Deserialize, Default)]
pub struct AuthData {
    pub users: HashMap<String, UserAuth>,
    pub revoked_tokens: HashMap<String, RevokedToken>,
}

/// 常量定义 (对应原文件的常量)
const ACCESS_TOKEN_EXPIRY: i64 = 15 * 60; // 15分钟
const REFRESH_TOKEN_EXPIRY: i64 = 30 * 24 * 60 * 60; // 30天
const ACCOUNT_LOCK_TIME: i64 = 10 * 60; // 10分钟
const MAX_LOGIN_ATTEMPTS: u32 = 5;

/// 全局认证状态
static AUTH_STATE: std::sync::OnceLock<std::sync::RwLock<AuthData>> = std::sync::OnceLock::new();
static JWT_KEYS: std::sync::OnceLock<(EncodingKey, DecodingKey)> = std::sync::OnceLock::new();

/// 获取认证数据
fn get_auth_data() -> &'static std::sync::RwLock<AuthData> {
    AUTH_STATE.get_or_init(|| std::sync::RwLock::new(AuthData::default()))
}

/// 获取JWT密钥
fn get_jwt_keys() -> Option<&'static (EncodingKey, DecodingKey)> {
    JWT_KEYS.get()
}

/// 认证服务
pub struct AuthService {
    encoding_key: EncodingKey,
    decoding_key: DecodingKey,
}

/// 初始化认证模块 (对应原文件的 initAuth)
pub async fn init_auth() -> Result<()> {
    info!("Initializing authentication module");

    // 生成或加载JWT密钥
    let secret = get_config().read().unwrap()
        .jwt_secret.clone()
        .unwrap_or_else(|| {
            let new_secret = Uuid::new_v4().to_string();
            // 保存新密钥到配置
            crate::config::update_config(|config| {
                config.jwt_secret = Some(new_secret.clone());
                Ok(())
            }).unwrap_or_else(|e| error!("Failed to save JWT secret: {}", e));
            new_secret
        });

    let encoding_key = EncodingKey::from_secret(secret.as_bytes());
    let decoding_key = DecodingKey::from_secret(secret.as_bytes());

    JWT_KEYS.set((encoding_key, decoding_key)).map_err(|_| {
        anyhow::anyhow!("Failed to set JWT keys")
    })?;

    // 加载认证数据
    load_auth_data()?;

    // 清理过期数据
    cleanup_revoked_tokens();
    cleanup_refresh_tokens();

    info!("Authentication module initialized successfully");
    Ok(())
}

/// 加载认证数据
fn load_auth_data() -> Result<()> {
    let auth_data_path = get_data_dir().join("auth.json");

    let auth_data: AuthData = if auth_data_path.exists() {
        load_json_file(&auth_data_path).unwrap_or_default()
    } else {
        AuthData::default()
    };

    if let Ok(mut data) = get_auth_data().write() {
        *data = auth_data;
    }

    Ok(())
}

/// 保存认证数据
fn save_auth_data() -> Result<()> {
    let auth_data = get_auth_data().read().unwrap().clone();
    let auth_data_path = get_data_dir().join("auth.json");

    save_json_file(&auth_data_path, &auth_data)?;
    Ok(())
}

/// 清理过期的撤销令牌 (对应原文件的 cleanupRevokedTokens)
fn cleanup_revoked_tokens() {
    if let Ok(mut auth_data) = get_auth_data().write() {
        let now = Utc::now();
        auth_data.revoked_tokens.retain(|_, token| token.expiry > now);
    }
}

/// 清理过期的刷新令牌 (对应原文件的 cleanupRefreshTokens)
fn cleanup_refresh_tokens() {
    if let Ok(mut auth_data) = get_auth_data().write() {
        let now = Utc::now();
        for user_auth in auth_data.users.values_mut() {
            user_auth.refresh_tokens.retain(|token| token.expiry > now);
        }
    }
}

impl AuthService {
    /// 创建新的认证服务
    pub fn new() -> Result<Self> {
        let (encoding_key, decoding_key) = get_jwt_keys()
            .ok_or_else(|| anyhow::anyhow!("JWT keys not initialized"))?;

        Ok(Self {
            encoding_key: encoding_key.clone(),
            decoding_key: decoding_key.clone(),
        })
    }
    
    /// 生成访问令牌 (对应原文件的 generateAccessToken)
    pub fn generate_access_token(&self, username: &str, user_id: &str) -> Result<String> {
        let now = Utc::now();
        let exp = now + Duration::seconds(ACCESS_TOKEN_EXPIRY);

        let claims = Claims {
            sub: username.to_string(),
            user_id: user_id.to_string(),
            jti: Uuid::new_v4().to_string(),
            iat: now.timestamp(),
            exp: exp.timestamp(),
            device_id: None,
        };

        let token = encode(&Header::default(), &claims, &self.encoding_key)?;
        Ok(token)
    }

    /// 生成刷新令牌 (对应原文件的 generateRefreshToken)
    pub fn generate_refresh_token(&self, username: &str, user_id: &str, device_id: &str) -> Result<String> {
        let now = Utc::now();
        let exp = now + Duration::seconds(REFRESH_TOKEN_EXPIRY);

        let claims = Claims {
            sub: username.to_string(),
            user_id: user_id.to_string(),
            jti: Uuid::new_v4().to_string(),
            iat: now.timestamp(),
            exp: exp.timestamp(),
            device_id: Some(device_id.to_string()),
        };

        let token = encode(&Header::default(), &claims, &self.encoding_key)?;
        Ok(token)
    }
    
    /// 验证JWT令牌 (对应原文件的 verifyToken)
    pub fn verify_token(&self, token: &str) -> Result<Claims> {
        let token_data = decode::<Claims>(
            token,
            &self.decoding_key,
            &Validation::default(),
        )?;

        // 检查令牌是否已被撤销
        let auth_data = get_auth_data().read().unwrap();
        if auth_data.revoked_tokens.contains_key(&token_data.claims.jti) {
            return Err(anyhow::anyhow!("Token has been revoked"));
        }

        Ok(token_data.claims)
    }
}

/// 密码哈希 (对应原文件的 hashPassword)
pub async fn hash_password(password: &str) -> Result<String> {
    let salt = SaltString::generate(&mut OsRng);
    let argon2 = Argon2::default();

    let password_hash = argon2.hash_password(password.as_bytes(), &salt)
        .map_err(|e| anyhow::anyhow!("Failed to hash password: {}", e))?;

    Ok(password_hash.to_string())
}

/// 验证密码 (对应原文件的 verifyPassword)
pub async fn verify_password(password: &str, hash: &str) -> Result<bool> {
    let parsed_hash = PasswordHash::new(hash)
        .map_err(|e| anyhow::anyhow!("Invalid password hash: {}", e))?;

    let argon2 = Argon2::default();
    Ok(argon2.verify_password(password.as_bytes(), &parsed_hash).is_ok())
}

/// 通过用户名获取用户 (对应原文件的 getUserByUsername)
pub fn get_user_by_username(username: &str) -> Option<UserAuth> {
    let auth_data = get_auth_data().read().unwrap();
    auth_data.users.get(username).cloned()
}
    
    /// 用户登录
    pub async fn login(&self, request: LoginRequest) -> Result<LoginResponse> {
        // TODO: 实现用户验证逻辑
        // 这里应该查询数据库验证用户名和密码
        
        // 临时实现
        if request.username == "admin" && request.password == "password" {
            let token = self.generate_token(&request.username, "admin")?;
            let user = User {
                id: request.username.clone(),
                username: request.username,
                email: "<EMAIL>".to_string(),
                display_name: "Administrator".to_string(),
                avatar_url: None,
                permission_level: fount_types::PermissionLevel::Admin,
                status: fount_types::Status::Active,
                created_at: Utc::now(),
                updated_at: Utc::now(),
                last_login: Some(Utc::now()),
            };
            
            Ok(LoginResponse {
                user,
                token,
                expires_at: Utc::now() + Duration::hours(24),
            })
        } else {
            Err(anyhow::anyhow!("Invalid credentials"))
        }
    }
}

/// 认证中间件
pub async fn auth_middleware(
    State(auth_service): State<Arc<AuthService>>,
    mut request: Request,
    next: Next,
) -> Result<Response, StatusCode> {
    let auth_header = request
        .headers()
        .get(AUTHORIZATION)
        .and_then(|header| header.to_str().ok());
    
    if let Some(auth_header) = auth_header {
        if let Some(token) = auth_header.strip_prefix("Bearer ") {
            match auth_service.verify_token(token) {
                Ok(claims) => {
                    // 将用户信息添加到请求扩展中
                    request.extensions_mut().insert(claims);
                    Ok(next.run(request).await)
                }
                Err(_) => Err(StatusCode::UNAUTHORIZED),
            }
        } else {
            Err(StatusCode::UNAUTHORIZED)
        }
    } else {
        Err(StatusCode::UNAUTHORIZED)
    }
}
