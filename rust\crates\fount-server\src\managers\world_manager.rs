//! 世界管理器
//! 对应原文件: src/server/managers/world_manager.mjs

use std::collections::HashMap;
use uuid::Uuid;
use anyhow::Result;
use fount_types::{World, CreateWorldRequest};

/// 世界管理器
pub struct WorldManager {
    worlds: HashMap<Uuid, World>,
}

impl WorldManager {
    pub fn new() -> Self {
        Self {
            worlds: HashMap::new(),
        }
    }
    
    pub async fn create_world(&mut self, request: CreateWorldRequest, creator_id: String) -> Result<World> {
        let world = World {
            id: Uuid::new_v4(),
            name: request.name,
            description: request.description,
            creator_id,
            status: fount_types::Status::Active,
            created_at: chrono::Utc::now(),
            updated_at: chrono::Utc::now(),
        };
        
        self.worlds.insert(world.id, world.clone());
        Ok(world)
    }
    
    pub fn get_world(&self, id: &Uuid) -> Option<&World> {
        self.worlds.get(id)
    }
    
    pub fn list_worlds(&self) -> Vec<&World> {
        self.worlds.values().collect()
    }
}

impl Default for WorldManager {
    fn default() -> Self {
        Self::new()
    }
}

/// 初始化世界管理器
pub async fn init() -> anyhow::Result<()> {
    tracing::info!("Initializing world manager");
    Ok(())
}

/// PartManager trait实现
#[async_trait::async_trait]
impl super::PartManager for WorldManager {
    /// 获取世界列表
    async fn get_part_list(&self, username: &str) -> anyhow::Result<Vec<String>> {
        // TODO: 根据用户名过滤世界列表
        let worlds: Vec<String> = self.worlds.values()
            .map(|w| w.name.clone())
            .collect();
        Ok(worlds)
    }

    /// 获取世界详情
    async fn get_part_details(&self, username: &str, name: &str, no_cache: bool) -> anyhow::Result<serde_json::Value> {
        // TODO: 根据用户名和世界名查找世界
        if let Some(world) = self.worlds.values().find(|w| w.name == name) {
            let details = serde_json::json!({
                "id": world.id,
                "name": world.name,
                "description": world.description,
                "creator_id": world.creator_id,
                "status": world.status,
                "created_at": world.created_at,
                "updated_at": world.updated_at
            });
            Ok(details)
        } else {
            Err(anyhow::anyhow!("World '{}' not found", name))
        }
    }

    /// 加载世界
    async fn load_part(&self, username: &str, name: &str) -> anyhow::Result<()> {
        tracing::info!("Loading world '{}' for user '{}'", name, username);
        // TODO: 实现世界加载逻辑
        Ok(())
    }

    /// 卸载世界
    async fn unload_part(&self, username: &str, name: &str) -> anyhow::Result<()> {
        tracing::info!("Unloading world '{}' for user '{}'", name, username);
        // TODO: 实现世界卸载逻辑
        Ok(())
    }
}
