//! 世界管理器
//! 对应原文件: src/server/managers/world_manager.mjs

use std::collections::HashMap;
use uuid::Uuid;
use anyhow::Result;
use fount_types::{World, CreateWorldRequest};

/// 世界管理器
pub struct WorldManager {
    worlds: HashMap<Uuid, World>,
}

impl WorldManager {
    pub fn new() -> Self {
        Self {
            worlds: HashMap::new(),
        }
    }
    
    pub async fn create_world(&mut self, request: CreateWorldRequest, creator_id: String) -> Result<World> {
        let world = World {
            id: Uuid::new_v4(),
            name: request.name,
            description: request.description,
            creator_id,
            status: fount_types::Status::Active,
            created_at: chrono::Utc::now(),
            updated_at: chrono::Utc::now(),
        };
        
        self.worlds.insert(world.id, world.clone());
        Ok(world)
    }
    
    pub fn get_world(&self, id: &Uuid) -> Option<&World> {
        self.worlds.get(id)
    }
    
    pub fn list_worlds(&self) -> Vec<&World> {
        self.worlds.values().collect()
    }
}

impl Default for WorldManager {
    fn default() -> Self {
        Self::new()
    }
}

/// 初始化世界管理器
pub async fn init() -> anyhow::Result<()> {
    tracing::info!("Initializing world manager");
    Ok(())
}
