//! 基础类型定义
//! 对应原文件: src/decl/basedefs.ts

use serde::{Deserialize, Serialize};
use std::collections::HashMap;
use uuid::Uuid;
use chrono::{DateTime, Utc};
use indexmap::IndexMap;

/// 时间戳类型 (对应 timeStamp_t)
pub type TimeStamp = i64;

/// 本地化字符串类型 (对应 locale_t)
pub type Locale = String;

/// 角色类型枚举 (对应 role_t)
#[derive(Debug, Clone, PartialEq, Eq, Hash, Serialize, Deserialize)]
#[serde(rename_all = "lowercase")]
pub enum Role {
    User,
    Char,
    System,
    World,
    Plugin,
}

impl std::fmt::Display for Role {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        match self {
            Role::User => write!(f, "user"),
            Role::Char => write!(f, "char"),
            Role::System => write!(f, "system"),
            Role::World => write!(f, "world"),
            Role::Plugin => write!(f, "plugin"),
        }
    }
}

/// 本地化信息结构 (对应 info_t 中的单个语言信息)
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct LocalizedInfo {
    pub name: String,
    pub avatar: String,
    pub description: String,
    pub description_markdown: String,
    pub version: String,
    pub author: String,
    pub homepage: String,
    pub issuepage: String,
    pub tags: Vec<String>,
}

/// 多语言信息类型 (对应 info_t)
pub type Info = IndexMap<Locale, LocalizedInfo>;

/// 状态枚举
#[derive(Debug, Clone, PartialEq, Eq, Serialize, Deserialize)]
#[serde(rename_all = "lowercase")]
pub enum Status {
    Active,
    Inactive,
    Pending,
    Error,
    Deleted,
}

/// 权限级别枚举
#[derive(Debug, Clone, PartialEq, Eq, Serialize, Deserialize)]
#[serde(rename_all = "lowercase")]
pub enum PermissionLevel {
    Admin,
    User,
    Guest,
    Banned,
}

/// 分页信息
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct Pagination {
    pub page: u32,
    pub per_page: u32,
    pub total: u64,
    pub total_pages: u32,
}

impl Pagination {
    pub fn new(page: u32, per_page: u32, total: u64) -> Self {
        let total_pages = ((total as f64) / (per_page as f64)).ceil() as u32;
        Self {
            page,
            per_page,
            total,
            total_pages,
        }
    }
}

/// 通用响应结构
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ApiResponse<T> {
    pub success: bool,
    pub data: Option<T>,
    pub error: Option<String>,
    pub timestamp: DateTime<Utc>,
}

impl<T> ApiResponse<T> {
    pub fn success(data: T) -> Self {
        Self {
            success: true,
            data: Some(data),
            error: None,
            timestamp: Utc::now(),
        }
    }

    pub fn error(error: String) -> Self {
        Self {
            success: false,
            data: None,
            error: Some(error),
            timestamp: Utc::now(),
        }
    }
}

/// 基础配置类型
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct BaseConfig {
    pub version: String,
    pub debug: bool,
    pub port: u16,
    pub host: String,
    pub data_dir: String,
    pub static_dir: String,
    pub log_level: String,
}

impl Default for BaseConfig {
    fn default() -> Self {
        Self {
            version: "0.1.0".to_string(),
            debug: false,
            port: 3000,
            host: "127.0.0.1".to_string(),
            data_dir: "data".to_string(),
            static_dir: "static".to_string(),
            log_level: "info".to_string(),
        }
    }
}

/// 用户会话信息
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SessionInfo {
    pub id: Uuid,
    pub user_id: Option<String>,
    pub created_at: DateTime<Utc>,
    pub last_active: DateTime<Utc>,
    pub ip_address: String,
    pub user_agent: Option<String>,
}

/// 错误信息结构
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ErrorInfo {
    pub code: String,
    pub message: String,
    pub details: Option<HashMap<String, serde_json::Value>>,
}

/// 通用结果类型
pub type Result<T> = std::result::Result<T, crate::FountError>;

impl<T> ApiResponse<T> {
    pub fn success(data: T) -> Self {
        Self {
            success: true,
            data: Some(data),
            error: None,
            timestamp: Utc::now(),
        }
    }
    
    pub fn error(message: String) -> Self {
        Self {
            success: false,
            data: None,
            error: Some(message),
            timestamp: Utc::now(),
        }
    }
}

/// 分页信息
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct Pagination {
    pub page: u32,
    pub per_page: u32,
    pub total: u64,
    pub total_pages: u32,
}

/// 文件信息
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct FileInfo {
    pub name: String,
    pub size: u64,
    pub mime_type: String,
    pub created_at: DateTime<Utc>,
    pub modified_at: DateTime<Utc>,
}

/// 权限级别
#[derive(Debug, Clone, Copy, Serialize, Deserialize, PartialEq, Eq)]
pub enum PermissionLevel {
    Guest,
    User,
    Moderator,
    Admin,
    Owner,
}

/// 状态枚举
#[derive(Debug, Clone, Copy, Serialize, Deserialize, PartialEq, Eq)]
pub enum Status {
    Active,
    Inactive,
    Pending,
    Suspended,
    Deleted,
}
