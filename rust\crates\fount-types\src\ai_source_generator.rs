//! AI源生成器类型定义
//! 对应原文件: src/decl/AIsourceGeneretor.ts

use serde::{Deserialize, Serialize};
use std::collections::HashMap;
use uuid::Uuid;
use chrono::{DateTime, Utc};
use async_trait::async_trait;
use crate::{ApiResponse, Status, Info, Locale, AiSource};

/// AI源生成器参数
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct AiSourceGeneratorArgs {
    pub username: String,
    pub save_config: bool,
}

/// AI源生成器信息接口 (对应 interfaces.info)
#[async_trait]
pub trait AiSourceGeneratorInfoInterface {
    async fn update_info(&self, locales: Vec<Locale>) -> crate::Result<Info>;
}

/// AI源生成器配置接口 (对应 interfaces.config)
#[async_trait]
pub trait AiSourceGeneratorConfigInterface {
    async fn get_data(&self) -> crate::Result<serde_json::Value>;
    async fn set_data(&self, data: serde_json::Value) -> crate::Result<()>;
}

/// AI源生成器AI源接口 (对应 interfaces.AIsource)
#[async_trait]
pub trait AiSourceGeneratorAiSourceInterface {
    async fn get_config_template(&self) -> crate::Result<serde_json::Value>;
    async fn get_source(&self, config: serde_json::Value, args: AiSourceGeneratorArgs) -> crate::Result<Box<dyn AiSource<serde_json::Value, serde_json::Value>>>;
}

/// AI源生成器主接口 (对应 AIsourceGenerator)
#[async_trait]
pub trait AiSourceGenerator {
    /// 获取信息
    fn get_info(&self) -> &Info;

    /// 初始化
    async fn init(&mut self) -> crate::Result<()>;

    /// 加载
    async fn load(&mut self) -> crate::Result<()>;

    /// 卸载
    async fn unload(&mut self) -> crate::Result<()>;

    /// 卸载安装
    async fn uninstall(&mut self) -> crate::Result<()>;

    /// 获取信息接口
    fn info_interface(&self) -> Option<&dyn AiSourceGeneratorInfoInterface>;

    /// 获取配置接口
    fn config_interface(&self) -> Option<&dyn AiSourceGeneratorConfigInterface>;

    /// 获取AI源接口
    fn ai_source_interface(&self) -> Option<&dyn AiSourceGeneratorAiSourceInterface>;
}

/// AI源生成器配置
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct AiSourceGeneratorConfig {
    pub id: Uuid,
    pub name: String,
    pub description: String,
    pub version: String,
    pub author: String,
    pub status: Status,
    pub created_at: DateTime<Utc>,
    pub updated_at: DateTime<Utc>,
    pub metadata: HashMap<String, serde_json::Value>,
}

/// 生成器配置模板
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct GeneratorConfigTemplate {
    pub template: String,
    pub parameters: serde_json::Value,
}

/// AI源生成器响应类型别名
pub type AiSourceGeneratorResponse = ApiResponse<AiSourceGeneratorConfig>;

/// 生成请求
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct GenerateRequest {
    pub generator_id: Uuid,
    pub config: GeneratorConfig,
}
