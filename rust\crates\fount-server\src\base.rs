//! 服务器基础模块
//! 对应原文件: src/server/base.mjs

use std::path::PathBuf;
use std::time::Instant;
use std::sync::OnceLock;
use anyhow::Result;

/// 项目根目录 (对应 __dirname)
static PROJECT_ROOT: OnceLock<PathBuf> = OnceLock::new();

/// 服务器启动时间 (对应 startTime)
static START_TIME: OnceLock<Instant> = OnceLock::new();

/// 获取项目根目录 (对应 __dirname)
pub fn get_project_root() -> &'static PathBuf {
    PROJECT_ROOT.get_or_init(|| {
        // 尝试从环境变量获取，否则使用当前目录
        std::env::var("FOUNT_ROOT")
            .map(PathBuf::from)
            .unwrap_or_else(|_| {
                std::env::current_dir()
                    .unwrap_or_else(|_| PathBuf::from("."))
            })
    })
}

/// 设置项目根目录
pub fn set_project_root(path: PathBuf) -> Result<()> {
    PROJECT_ROOT.set(path).map_err(|_| {
        anyhow::anyhow!("Project root already set")
    })
}

/// 设置启动时间 (对应 set_start)
pub fn set_start_time() {
    START_TIME.set(Instant::now()).ok();
}

/// 获取启动时间 (对应 startTime)
pub fn get_start_time() -> Option<&'static Instant> {
    START_TIME.get()
}

/// 获取运行时长（秒）
pub fn get_uptime_seconds() -> f64 {
    get_start_time()
        .map(|start| start.elapsed().as_secs_f64())
        .unwrap_or(0.0)
}

/// 获取运行时长（毫秒）
pub fn get_uptime_millis() -> u128 {
    get_start_time()
        .map(|start| start.elapsed().as_millis())
        .unwrap_or(0)
}

/// 获取数据目录路径
pub fn get_data_dir() -> PathBuf {
    get_project_root().join("data")
}

/// 获取静态文件目录路径
pub fn get_static_dir() -> PathBuf {
    get_project_root().join("src").join("public")
}

/// 获取配置文件路径
pub fn get_config_path() -> PathBuf {
    get_data_dir().join("config.json")
}

/// 获取默认配置文件路径
pub fn get_default_config_path() -> PathBuf {
    get_project_root().join("default").join("config.json")
}

/// 确保数据目录存在
pub fn ensure_data_dir() -> Result<()> {
    let data_dir = get_data_dir();
    if !data_dir.exists() {
        std::fs::create_dir_all(&data_dir)?;
    }
    Ok(())
}

/// 初始化基础环境
pub fn init_base() -> Result<()> {
    // 设置启动时间
    set_start_time();

    // 确保数据目录存在
    ensure_data_dir()?;

    tracing::info!("Base environment initialized");
    tracing::debug!("Project root: {}", get_project_root().display());
    tracing::debug!("Data directory: {}", get_data_dir().display());

    Ok(())
}
