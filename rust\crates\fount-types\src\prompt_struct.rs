//! 提示结构类型定义
//! 对应原文件: src/decl/prompt_struct.ts

use serde::{Deserialize, Serialize};
use std::collections::HashMap;
use uuid::Uuid;
use chrono::{DateTime, Utc};
use crate::{Role, TimeStamp};

/// 文本内容
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct TextContent {
    pub content: String,
    pub description: String,
    pub important: u32,
}

/// 文件信息
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct FileInfo {
    pub name: String,
    pub mime_type: String,
    pub buffer: Vec<u8>,
    pub description: String,
}

/// 聊天日志条目 (对应 chatLogEntry_t)
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ChatLogEntry {
    pub name: String,
    pub timestamp: TimeStamp,
    pub role: Role,
    pub content: String,
    pub files: Vec<FileInfo>,
    pub extension: HashMap<String, serde_json::Value>,
}

/// 单部分提示 (对应 single_part_prompt_t)
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SinglePartPrompt {
    pub text: Vec<TextContent>,
    pub additional_chat_log: Vec<ChatLogEntry>,
    pub extension: HashMap<String, serde_json::Value>,
}

/// 其他角色提示 (对应 other_chars_prompt_t)
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct OtherCharsPrompt {
    pub name: String,
    pub is_active: bool,
    pub last_active: TimeStamp,
    pub text: Vec<TextContent>,
    pub additional_chat_log: Vec<ChatLogEntry>,
    pub extension: HashMap<String, serde_json::Value>,
}

/// 提示结构 (对应 prompt_struct_t)
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct PromptStruct {
    pub char_id: String,
    pub charname: String,
    pub alternative_charnames: Vec<String>,
    pub user_charname: String,
    pub user_prompt: SinglePartPrompt,
    pub char_prompt: SinglePartPrompt,
    pub other_chars_prompt: HashMap<String, OtherCharsPrompt>,
    pub world_prompt: SinglePartPrompt,
    pub plugin_prompts: HashMap<String, SinglePartPrompt>,
    pub chat_log: Vec<ChatLogEntry>,
}

/// 提示模板
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct PromptTemplate {
    pub id: Uuid,
    pub name: String,
    pub template: String,
    pub variables: Vec<String>,
    pub created_at: DateTime<Utc>,
    pub updated_at: DateTime<Utc>,
}

/// 提示参数
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct PromptParams {
    pub template_id: Uuid,
    pub variables: serde_json::Value,
}
